// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/model/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// KsAdvertiserStrategyTaskInfoRes is the golang structure for table ks_advertiser_strategy_task.
type KsAdvertiserStrategyTaskInfoRes struct {
	gmeta.Meta    `orm:"table:ks_advertiser_strategy_task"`
	TaskId        string      `orm:"task_id,primary" json:"taskId" dc:"任务ID"`                                                                                                                                                              // 任务ID
	TaskName      string      `orm:"task_name" json:"taskName" dc:"任务名称"`                                                                                                                                                                  // 任务名称
	TaskStatus    string      `orm:"task_status" json:"taskStatus" dc:"任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH"` // 任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH
	AdvertiserIds string      `orm:"advertiser_ids" json:"advertiserIds" dc:"广告主ID列表"`                                                                                                                                                     // 广告主ID列表
	CampaignNum   int         `orm:"campaign_num" json:"campaignNum" dc:"广告计划数"`                                                                                                                                                           // 广告计划数
	UnitNum       int         `orm:"unit_num" json:"unitNum" dc:"广告组数"`                                                                                                                                                                    // 广告组数
	RuleType      int         `orm:"rule_type" json:"ruleType" dc:"提交规则类型 1：立即提交 2：定时提交"`                                                                                                                                                  // 提交规则类型 1：立即提交 2：定时提交
	UserId        int         `orm:"user_id" json:"userId" dc:"归属人员"`                                                                                                                                                                      // 归属人员
	CreatedAt     *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                                                                                                                                                                // 创建时间
	UpdatedAt     *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                                                                                                                                                                // 更新时间
	DeletedAt     *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                                                                                                                                                                // 删除时间
}

type GroupStat struct {
	TaskId string `json:"taskId" dc:"任务ID"`
	Status string `json:"status" dc:"状态"`
	Count  int    `json:"count" dc:"数量"`
}

type KsAdvertiserStrategyTaskListRes struct {
	TaskId        string `json:"taskId" dc:"任务ID"`
	TaskName      string `json:"taskName" dc:"任务名称"`
	TaskStatus    string `json:"taskStatus" dc:"任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH"`
	AdvertiserIds string `json:"advertiserIds" dc:"广告主ID列表"`
	CampaignNum   int    `json:"campaignNum" dc:"广告计划数"`
	UnitNum       int    `json:"unitNum" dc:"广告组数"`
	RuleType      int    `json:"ruleType" dc:"提交规则类型 1：立即提交 2：定时提交"`
	UserId        int    `json:"userId" dc:"归属人员"`
	UserName      string `json:"userName" dc:"归属人员名称"`
	//成功数量
	SuccessNum int `json:"successNum" dc:"成功数量"`
	// 失败数量
	FailNum   int         `json:"failNum" dc:"失败数量"`
	InitNum   int         `json:"initNum" dc:"初始化"`
	CreatedAt *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// KsAdvertiserStrategyTaskSearchReq 分页请求参数
type KsAdvertiserStrategyTaskSearchReq struct {
	comModel.PageReq
	TaskId        string `p:"taskId" dc:"任务ID"`                                                                                                                                                                  //任务ID
	TaskName      string `p:"taskName" dc:"任务名称"`                                                                                                                                                                //任务名称
	TaskStatus    string `p:"taskStatus" dc:"任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH"` //任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH
	AdvertiserIds string `p:"advertiserIds" dc:"广告主ID列表"`                                                                                                                                                        //广告主ID列表
	CampaignNum   string `p:"campaignNum" v:"campaignNum@integer#广告计划数需为整数" dc:"广告计划数"`                                                                                                                          //广告计划数
	UnitNum       string `p:"unitNum" v:"unitNum@integer#广告组数需为整数" dc:"广告组数"`                                                                                                                                    //广告组数
	RuleType      string `p:"ruleType" v:"ruleType@integer#提交规则类型 1：立即提交 2：定时提交需为整数" dc:"提交规则类型 1：立即提交 2：定时提交"`                                                                                                  //提交规则类型 1：立即提交 2：定时提交
	UserId        string `p:"userId" v:"userId@integer#归属人员需为整数" dc:"归属人员"`                                                                                                                                      //归属人员
	CreatedAt     string `p:"createdAt" v:"createdAt@datetime#创建时间需为YYYY-MM-DD hh:mm:ss格式" dc:"创建时间"`                                                                                                            //创建时间
}

// KsAdvertiserStrategyTaskSearchRes 列表返回结果
type KsAdvertiserStrategyTaskSearchRes struct {
	comModel.ListRes
	List []*KsAdvertiserStrategyTaskListRes `json:"list"`
}

// KsAdvertiserStrategyTaskAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskAddReq struct {
	TaskId        string `p:"taskId" v:"required#主键ID不能为空" dc:"任务ID"`
	TaskName      string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	TaskStatus    string `p:"taskStatus" v:"required#任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH不能为空" dc:"任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH"`
	AdvertiserIds string `p:"advertiserIds"  dc:"广告主ID列表"`
	CampaignNum   int    `p:"campaignNum"  dc:"广告计划数"`
	UnitNum       int    `p:"unitNum"  dc:"广告组数"`
	RuleType      int    `p:"ruleType"  dc:"提交规则类型 1：立即提交 2：定时提交"`
	UserId        int    `p:"userId"  dc:"归属人员"`
}

type KsAdvertiserStrategyTaskUpdateNameReq struct {
	TaskId   string `p:"taskId" v:"required#主键ID不能为空" dc:"任务ID"`
	TaskName string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
}

// KsAdvertiserStrategyTaskEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskEditReq struct {
	TaskId        string `p:"taskId" v:"required#主键ID不能为空" dc:"任务ID"`
	TaskName      string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	TaskStatus    string `p:"taskStatus" v:"required#任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH不能为空" dc:"任务状态 等待提交：INIT 素材上传中：MATERIAL_PROCESSING 项目等待提交：PROJECT_INIT 项目提交中：PROJECT_PROCESSING 广告等待提交：PROMOTION_INIT 广告提交中：PROMOTION_PROCESSING 已终止：TERMINATED 提交完成：FINISH"`
	AdvertiserIds string `p:"advertiserIds"  dc:"广告主ID列表"`
	CampaignNum   int    `p:"campaignNum"  dc:"广告计划数"`
	UnitNum       int    `p:"unitNum"  dc:"广告组数"`
	RuleType      int    `p:"ruleType"  dc:"提交规则类型 1：立即提交 2：定时提交"`
	UserId        int    `p:"userId"  dc:"归属人员"`
}

type AdExecuteTaskReq struct {
	TaskName string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	RuleType int    `p:"ruleType"  dc:"提交规则类型 1：立即提交 2：定时提交"`
	// 拆分之后的数据
	Generate *KsAdvertiserStrategyGenerateRes `json:"generate" dc:"生成信息"`
	// 拆分之前的策略组数据数据
	StrategyConfig *KsAdvertiserStrategyGenerateReq `json:"strategyConfig" dc:"策略组数据"`
}
