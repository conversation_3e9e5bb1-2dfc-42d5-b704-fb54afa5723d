// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/model/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdConditionGroupConfigInfoRes is the golang structure for table ad_condition_group_config.
type AdConditionGroupConfigInfoRes struct {
	gmeta.Meta     `orm:"table:ad_condition_group_config"`
	Id             int64            `orm:"id,primary" json:"id" dc:""`                                //
	Name           string           `orm:"name" json:"name" dc:"组合名称"`                                // 组合名称
	ConditionGroup []ConditionGroup `orm:"condition_group" json:"conditionGroup" dc:"查询条件组合"`         // 查询条件组合
	OptType        string           `orm:"opt_type" json:"optType" dc:"查询条件组合类型 所有：and 任一：or"`        // 查询条件组合类型 所有：and 任一：or
	Modules        string           `orm:"modules" json:"modules" dc:"操作模块：项目：PROJECT  广告：PROMOTION"` // 操作模块：项目：PROJECT  广告：PROMOTION
	UserId         int              `orm:"user_id" json:"userId" dc:"用户ID"`                           // 用户ID
	CreatedAt      *gtime.Time      `orm:"created_at" json:"createdAt" dc:"创建时间"`                     // 创建时间
	UpdatedAt      *gtime.Time      `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                     // 更新时间
	DeletedAt      *gtime.Time      `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                     // 删除时间
}

type AdConditionGroupConfigListRes struct {
	Id             int64            `json:"id" dc:""`
	Name           string           `json:"name" dc:"组合名称"`
	ConditionGroup []ConditionGroup `json:"conditionGroup" dc:"查询条件组合"`
	OptType        string           `json:"optType" dc:"查询条件组合类型 所有：and 任一：or"`
	Modules        string           `json:"modules" dc:"操作模块：项目：PROJECT  广告：PROMOTION"`
	UserId         int              `json:"userId" dc:"用户ID"`
	CreatedAt      *gtime.Time      `json:"createdAt" dc:"创建时间"`
}

// AdConditionGroupConfigSearchReq 分页请求参数
type AdConditionGroupConfigSearchReq struct {
	comModel.PageReq
	Name    string `p:"name" dc:"组合名称"`                             //组合名称
	OptType string `p:"optType" dc:"查询条件组合类型 所有：and 任一：or"`         //查询条件组合类型 所有：and 任一：or
	Modules string `p:"modules" dc:"操作模块：项目：PROJECT  广告：PROMOTION"` //操作模块：项目：PROJECT  广告：PROMOTION
}

// AdConditionGroupConfigSearchRes 列表返回结果
type AdConditionGroupConfigSearchRes struct {
	comModel.ListRes
	List []*AdConditionGroupConfigListRes `json:"list"`
}

// AdConditionGroupConfigAddReq 添加操作请求参数
type AdConditionGroupConfigAddReq struct {
	Name           string           `p:"name" v:"required#组合名称不能为空" dc:"组合名称"`
	ConditionGroup []ConditionGroup `p:"conditionGroup"  dc:"查询条件组合"`
	OptType        string           `p:"optType"  dc:"查询条件组合类型 所有：and 任一：or"`
	Modules        string           `p:"modules"  dc:"操作模块：项目：PROJECT  广告：PROMOTION"`
}

// AdConditionGroupConfigEditReq 修改操作请求参数
type AdConditionGroupConfigEditReq struct {
	Id             int64            `p:"id" v:"required#主键ID不能为空" dc:""`
	Name           string           `p:"name" v:"required#组合名称不能为空" dc:"组合名称"`
	ConditionGroup []ConditionGroup `p:"conditionGroup"  dc:"查询条件组合"`
	OptType        string           `p:"optType"  dc:"查询条件组合类型 所有：and 任一：or"`
	Modules        string           `p:"modules"  dc:"操作模块：项目：PROJECT  广告：PROMOTION"`
}

type ConditionGroup struct {
	Field  string `p:"field" json:"field" dc:"指标"`
	Symbol string `p:"symbol" json:"symbol" dc:"运算符"`
	Value  string `p:"value" json:"value" dc:"值"`
}
