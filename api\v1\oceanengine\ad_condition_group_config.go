// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: api/v1/oceanengine/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package oceanengine

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

// AdConditionGroupConfigSearchReq 分页请求参数
type AdConditionGroupConfigSearchReq struct {
	g.Meta `path:"/list" tags:"广告筛选条件组合配置" method:"post" summary:"广告筛选条件组合配置列表"`
	commonApi.Author
	model.AdConditionGroupConfigSearchReq
}

// AdConditionGroupConfigSearchRes 列表返回结果
type AdConditionGroupConfigSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.AdConditionGroupConfigSearchRes
}

// AdConditionGroupConfigAddReq 添加操作请求参数
type AdConditionGroupConfigAddReq struct {
	g.Meta `path:"/add" tags:"广告筛选条件组合配置" method:"post" summary:"广告筛选条件组合配置添加"`
	commonApi.Author
	*model.AdConditionGroupConfigAddReq
}

// AdConditionGroupConfigAddRes 添加操作返回结果
type AdConditionGroupConfigAddRes struct {
	commonApi.EmptyRes
}

// AdConditionGroupConfigEditReq 修改操作请求参数
type AdConditionGroupConfigEditReq struct {
	g.Meta `path:"/edit" tags:"广告筛选条件组合配置" method:"put" summary:"广告筛选条件组合配置修改"`
	commonApi.Author
	*model.AdConditionGroupConfigEditReq
}

// AdConditionGroupConfigEditRes 修改操作返回结果
type AdConditionGroupConfigEditRes struct {
	commonApi.EmptyRes
}

// AdConditionGroupConfigGetReq 获取一条数据请求
type AdConditionGroupConfigGetReq struct {
	g.Meta `path:"/get" tags:"广告筛选条件组合配置" method:"get" summary:"获取广告筛选条件组合配置信息"`
	commonApi.Author
	Id int64 `p:"id" v:"required#主键必须"` //通过主键获取
}

// AdConditionGroupConfigGetRes 获取一条数据结果
type AdConditionGroupConfigGetRes struct {
	g.Meta `mime:"application/json"`
	*model.AdConditionGroupConfigInfoRes
}

// AdConditionGroupConfigDeleteReq 删除数据请求
type AdConditionGroupConfigDeleteReq struct {
	g.Meta `path:"/delete" tags:"广告筛选条件组合配置" method:"post" summary:"删除广告筛选条件组合配置"`
	commonApi.Author
	Ids []int64 `p:"ids" v:"required#主键必须"` //通过主键删除
}

// AdConditionGroupConfigDeleteRes 删除数据返回
type AdConditionGroupConfigDeleteRes struct {
	commonApi.EmptyRes
}
