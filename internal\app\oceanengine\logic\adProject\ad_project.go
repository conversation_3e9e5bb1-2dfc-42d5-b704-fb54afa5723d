// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2024-11-16 10:32:43
// 生成路径: internal/app/oceanengine/logic/ad_project.go
// 生成人：cq
// desc:巨量项目表
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	toutiaoModel "github.com/tiger1103/gfast/v3/library/advertiser/toutiao/model"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"github.com/tiger1103/gfast/v3/library/libUtils"
	"github.com/tiger1103/gfast/v3/library/liberr"
	"strconv"
	"strings"
)

func init() {
	service.RegisterAdProject(New())
}

func New() service.IAdProject {
	return &sAdProject{}
}

type sAdProject struct{}

func (s *sAdProject) List(ctx context.Context, req *model.AdProjectSearchReq) (listRes *model.AdProjectSearchRes, err error) {
	listRes = new(model.AdProjectSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdProjectAnalytic.Ctx(ctx)
		if !admin && len(userIds) > 0 {
			m = m.WhereIn(dao.AdAdvertiserAccount.Columns().UserId, userIds)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.AdProject.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdProject.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.Status != "" {
			m = m.Where(dao.AdProject.Columns().Status+" = ?", req.Status)
		}
		if req.Ids != nil && len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdProject.Columns().ProjectId, req.Ids)
		}
		if req.Keyword != "" {
			m = m.WhereLike(dao.AdProject.Columns().Name, "%"+req.Keyword+"%")
		}
		if req.StartTime != "" && req.EndTime != "" {
			dayStartTime, dayEndTime := libUtils.GetDayStartAndEnd(req.StartTime, req.EndTime)
			m = m.WhereGTE(dao.AdProject.Columns().ProjectCreateTime, dayStartTime)
			m = m.WhereLTE(dao.AdProject.Columns().ProjectCreateTime, dayEndTime)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdProjectListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdProjectListRes, len(res))
		for k, v := range res {
			listRes.List[k] = v
		}
	})
	return
}

// GetTaskList 给计划任务执行的list
func (s *sAdProject) GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserProjectTaskSearchRes, err error) {
	listRes = new(model.AdAdvertiserProjectTaskSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdProjectAnalytic.Ctx(ctx)
		if req.UserId > 0 {
			m = m.Where(dao.AdProject.Columns().UserId, req.UserId)
		}
		if req.AdStatus != "" {
			m = m.Where(dao.AdProject.Columns().Status, gconv.Int(req.AdStatus))
		}
		if len(req.Ids) > 0 {
			m = m.WhereIn(dao.AdProject.Columns().ProjectId, req.Ids)
		}
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.MaxPageSize
		}
		var res []*model.AdProjectListRes
		err = m.Fields("id as id").
			Fields("advertiser_id as advertiserId").
			Fields("project_id as projectId").
			Page(req.PageNum, req.PageSize).Order("id desc").Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdAdvertiserTaskListRes, 0)
		for _, item := range res {
			have := false
			for _, taskListRes := range listRes.List {
				if taskListRes.AdvertiserId == item.AdvertiserId {
					taskListRes.ObjectIds = append(taskListRes.ObjectIds, item.ProjectId)
					have = true
				}
			}
			if !have {
				listRes.List = append(listRes.List, &model.AdAdvertiserTaskListRes{
					AdvertiserId: item.AdvertiserId,
					ObjectIds:    []string{item.ProjectId},
				})
			}
		}
	})
	return
}

func (s *sAdProject) GetExportData(ctx context.Context, req *model.AdProjectSearchReq) (listRes []*model.AdProjectInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdProjectAnalytic.Ctx(ctx).WithAll()
		if req.ProjectId != "" {
			m = m.Where(dao.AdProject.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdProject.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if len(req.DateRange) != 0 {
			m = m.Where(dao.AdProject.Columns().CreatedAt+" >=? AND "+dao.AdProject.Columns().CreatedAt+" <=?", req.DateRange[0], req.DateRange[1])
		}
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id asc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdProject) GetById(ctx context.Context, id int64) (res *model.AdProjectInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdProject.Ctx(ctx).WithAll().Where(dao.AdProject.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdProject) GetByProjectId(ctx context.Context, projectId string) (res *model.AdProjectInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdProject.Ctx(ctx).WithAll().Where(dao.AdProject.Columns().ProjectId, projectId).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdProject) GetByProjectIds(ctx context.Context, projectIds []string) (res []*model.AdProjectInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdProject.Ctx(ctx).WithAll().WhereIn(dao.AdProject.Columns().ProjectId, projectIds).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdProject) GetMapByProjectIds(ctx context.Context, projectIds []string) (res map[string][]*model.AdProjectInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		res = make(map[string][]*model.AdProjectInfoRes)
		projectList, err1 := service.AdProject().GetByProjectIds(ctx, projectIds)
		liberr.ErrIsNil(ctx, err1, "获取信息失败")
		for _, v := range projectList {
			res[v.AdvertiserId] = append(res[v.AdvertiserId], v)
		}
	})
	return
}

func (s *sAdProject) GetProjectNumByAdvertiserId(ctx context.Context, advertiserId string) (count int, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
		liberr.ErrIsNil(ctx, err1, "获取access_token失败")
		advertiserIdInt64, _ := strconv.ParseInt(advertiserId, 10, 64)
		projectListGetRes, err2 := advertiser.GetToutiaoApiClient().ProjectListV3ApiService.AccessToken(tokenRes.AccessToken).
			ProjectListGetV3Request(toutiaoModel.ProjectListGetV3Request{AdvertiserId: &advertiserIdInt64}).
			Do()
		liberr.ErrIsNil(ctx, err2, "获取广告列表失败")
		if projectListGetRes.Data != nil && projectListGetRes.Data.PageInfo != nil {
			count = gconv.Int(*projectListGetRes.Data.PageInfo.TotalNumber)
		}
	})
	return
}

// GetProjectBudget 批量获取广告的预算Budget
func (s *sAdProject) GetProjectBudget(ctx context.Context, projectIds []string) (budgetMap map[string]float64, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		promotionList := make([]*model.AdProjectInfoRes, 0)
		dao.AdProject.Ctx(ctx).WithAll().Fields(dao.AdProject.Columns().ProjectId, dao.AdProject.Columns().Budget).WhereIn(dao.AdProject.Columns().ProjectId, projectIds).Scan(&promotionList)
		// 将promotionList 转化成 capBidMap
		budgetMap = make(map[string]float64)
		for _, promotion := range promotionList {
			budgetMap[promotion.ProjectId] = promotion.Budget
		}
	})
	return
}

func (s *sAdProject) GetAdProjectName(ctx context.Context, projectIds []string) (nameMap map[string]string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		accounts := make([]*model.AdProjectInfoRes, 0)
		dao.AdProject.Ctx(ctx).WithAll().Fields(dao.AdProject.Columns().AdvertiserId, dao.AdProject.Columns().Name).WhereIn(dao.AdProject.Columns().ProjectId, projectIds).Scan(&accounts)

		nameMap = make(map[string]string)
		for _, account := range accounts {
			nameMap[account.AdvertiserId] = account.Name
		}
	})
	return
}

func (s *sAdProject) Add(ctx context.Context, req *model.AdProjectAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).Insert(do.AdProject{
			ProjectId:                    req.ProjectId,
			AdvertiserId:                 req.AdvertiserId,
			DeliveryMode:                 req.DeliveryMode,
			DeliveryType:                 req.DeliveryType,
			LandingType:                  req.LandingType,
			MarketingGoal:                req.MarketingGoal,
			AdType:                       req.AdType,
			OptStatus:                    req.OptStatus,
			Name:                         req.Name,
			ProjectCreateTime:            req.ProjectCreateTime,
			ProjectModifyTime:            req.ProjectModifyTime,
			Status:                       req.Status,
			Pricing:                      req.Pricing,
			BudgetMode:                   req.BudgetMode,
			Budget:                       req.Budget,
			CpaBid:                       req.CpaBid,
			DeepCpaBid:                   req.DeepCpaBid,
			UserId:                       req.UserId,
			MajordomoAdvertiserAccountId: req.MajordomoAdvertiserAccountId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdProject) BatchAdd(ctx context.Context, batchAddReq []*model.AdProjectAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		projectIds := make([]string, 0)
		for _, v := range batchAddReq {
			projectIds = append(projectIds, v.ProjectId)
		}
		existProjects, _ := s.GetByProjectIds(ctx, projectIds)
		addReq := make([]*do.AdProject, 0)
		for _, req := range batchAddReq {
			adProject := &do.AdProject{
				ProjectId:                    req.ProjectId,
				AdvertiserId:                 req.AdvertiserId,
				DeliveryMode:                 req.DeliveryMode,
				DeliveryType:                 req.DeliveryType,
				LandingType:                  req.LandingType,
				MarketingGoal:                req.MarketingGoal,
				AdType:                       req.AdType,
				OptStatus:                    req.OptStatus,
				Name:                         req.Name,
				ProjectCreateTime:            req.ProjectCreateTime,
				ProjectModifyTime:            req.ProjectModifyTime,
				Status:                       req.Status,
				Pricing:                      req.Pricing,
				BudgetMode:                   req.BudgetMode,
				Budget:                       req.Budget,
				CpaBid:                       req.CpaBid,
				DeepCpaBid:                   req.DeepCpaBid,
				BidType:                      req.BidType,
				DeepBidType:                  req.DeepBidType,
				OptimizeGoal:                 req.OptimizeGoal,
				DeliveryRange:                req.DeliveryRange,
				DeliveryMedium:               req.DeliveryMedium,
				DeliveryProduct:              req.DeliveryProduct,
				MicroPromotionType:           req.MicroPromotionType,
				MicroAppInstanceId:           req.MicroAppInstanceId,
				UserId:                       req.UserId,
				MajordomoAdvertiserAccountId: req.MajordomoAdvertiserAccountId,
			}
			for _, existProject := range existProjects {
				if existProject.ProjectId == adProject.ProjectId {
					adProject.Id = existProject.Id
					break
				}
			}
			addReq = append(addReq, adProject)
		}
		_, err = dao.AdProject.Ctx(ctx).Save(addReq)
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdProject) Edit(ctx context.Context, req *model.AdProjectEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).WherePri(req.Id).Update(do.AdProject{
			ProjectId:         req.ProjectId,
			AdvertiserId:      req.AdvertiserId,
			DeliveryMode:      req.DeliveryMode,
			DeliveryType:      req.DeliveryType,
			LandingType:       req.LandingType,
			MarketingGoal:     req.MarketingGoal,
			AdType:            req.AdType,
			OptStatus:         req.OptStatus,
			Name:              req.Name,
			ProjectCreateTime: req.ProjectCreateTime,
			ProjectModifyTime: req.ProjectModifyTime,
			Status:            req.Status,
			Pricing:           req.Pricing,
			BudgetMode:        req.BudgetMode,
			Budget:            req.Budget,
			CpaBid:            req.CpaBid,
			DeepCpaBid:        req.DeepCpaBid,
			UserId:            req.UserId,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdProject) UpdateOptStatus(ctx context.Context, projectId string, optStatus string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).Where(dao.AdProject.Columns().ProjectId, projectId).
			Update(do.AdProject{
				OptStatus: optStatus,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdProject) UpdateCpaBid(ctx context.Context, projectId string, cpaBid float64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).Where(dao.AdProject.Columns().ProjectId, projectId).
			Update(do.AdProject{
				CpaBid: cpaBid,
			})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdProject) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).Delete(dao.AdProject.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdProject) DeleteByProjectId(ctx context.Context, projectId string) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdProject.Ctx(ctx).Delete(dao.AdProject.Columns().ProjectId, projectId)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}

func (s *sAdProject) RunSyncAdProject(ctx context.Context, req *model.SyncAdProjectReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		if req.StartTime == "" && req.EndTime == "" {
			_, err = s.SyncAdProject(ctx, req.StartTime, req.AdvertiserIds, nil)
			liberr.ErrIsNil(ctx, err, "同步广告项目失败")
		} else {
			startTime := req.StartTime
			endTime := req.EndTime
			for {
				if startTime > endTime {
					break
				}
				_, errors := s.SyncAdProject(ctx, req.StartTime, req.AdvertiserIds, nil)
				if errors != nil {
					g.Log().Error(ctx, errors)
				}
				startTime = libUtils.PlusDays(startTime, 1)
			}
		}
	})
	return
}

func (s *sAdProject) SyncAdProject(ctx context.Context, statDate string, advertiserIds []string, projectIds []int64) (tokenMap map[string]string, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		tokenMap = make(map[string]string)
		for _, advertiserId := range advertiserIds {
			accessTokenRes, err1 := service.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, advertiserId)
			if err1 != nil || accessTokenRes == nil || accessTokenRes.AccessToken == "" {
				g.Log().Errorf(ctx, fmt.Sprintf("广告主: %s, 获取access_token失败", advertiserId))
				continue
			}
			tokenMap[advertiserId] = accessTokenRes.AccessToken
			advertiserIdInt64, _ := strconv.ParseInt(advertiserId, 10, 64)
			var pageNo int64 = 1
			var pageSize int64 = 100
			for {
				request := toutiaoModel.ProjectListGetV3Request{
					AdvertiserId: &advertiserIdInt64,
					Page:         &pageNo,
					PageSize:     &pageSize,
				}
				var filtering = &models.ProjectListV30Filtering{}
				if statDate != "" {
					filtering.ProjectCreateTime = &statDate
				}
				if projectIds != nil && len(projectIds) > 0 {
					filtering.Ids = projectIds
				}
				request.Filtering = filtering
				projectListGetRes, err2 := advertiser.GetToutiaoApiClient().ProjectListV3ApiService.
					AccessToken(accessTokenRes.AccessToken).ProjectListGetV3Request(request).Do()
				if err2 != nil {
					g.Log().Error(ctx, fmt.Sprintf("获取广告项目列表失败: %v", err2))
					break
				}
				if projectListGetRes.Data == nil || projectListGetRes.Data.List == nil || len(projectListGetRes.Data.List) == 0 {
					break
				}
				projectList := make([]*model.AdProjectAddReq, 0)
				for _, listInner := range projectListGetRes.Data.List {
					adProjectAdd := &model.AdProjectAddReq{
						ProjectId:                    strconv.FormatInt(*listInner.ProjectId, 10),
						AdvertiserId:                 strconv.FormatInt(*listInner.AdvertiserId, 10),
						DeliveryMode:                 gconv.String(listInner.DeliveryMode),
						DeliveryType:                 gconv.String(listInner.DeliveryType),
						LandingType:                  gconv.String(listInner.LandingType),
						MarketingGoal:                gconv.String(listInner.MarketingGoal),
						AdType:                       gconv.String(listInner.AdType),
						OptStatus:                    gconv.String(listInner.OptStatus),
						Name:                         *listInner.Name,
						ProjectCreateTime:            *listInner.ProjectCreateTime,
						ProjectModifyTime:            *listInner.ProjectModifyTime,
						Status:                       gconv.String(listInner.Status),
						UserId:                       accessTokenRes.UserId,
						MajordomoAdvertiserAccountId: accessTokenRes.ParentId,
						Pricing:                      gconv.String(listInner.Pricing),
						BudgetMode:                   gconv.String(listInner.DeliverySetting.BudgetMode),
						Budget:                       gconv.Float64(listInner.DeliverySetting.Budget),
						CpaBid:                       gconv.Float64(listInner.DeliverySetting.CpaBid),
						DeepCpaBid:                   gconv.Float64(listInner.DeliverySetting.DeepCpabid),
						BidType:                      gconv.String(listInner.DeliverySetting.BidType),
						DeepBidType:                  gconv.String(listInner.DeliverySetting.DeepBidType),
						OptimizeGoal:                 listInner.OptimizeGoal,
						DeliveryRange:                listInner.DeliveryRange,
						DeliveryMedium:               gconv.String(listInner.DeliveryMedium),
						DeliveryProduct:              gconv.String(listInner.DeliveryProduct),
						MicroPromotionType:           gconv.String(listInner.MicroPromotionType),
						MicroAppInstanceId:           gconv.String(listInner.MicroAppInstanceId),
					}
					projectList = append(projectList, adProjectAdd)
				}
				err3 := s.BatchAdd(ctx, projectList)
				if err3 != nil {
					g.Log().Error(ctx, fmt.Sprintf("批量添加广告项目失败: %v", err3))
					break
				}
				if *projectListGetRes.Data.PageInfo.TotalPage <= pageNo {
					break
				}
				pageNo++
			}
			// 更新项目同步时间
			_, err4 := dao.AdAdvertiserAccount.Ctx(ctx).Where(dao.AdAdvertiserAccount.Columns().AdvertiserId, advertiserId).
				Update(g.Map{
					"sync_project_at": gtime.Now(),
				})
			liberr.ErrIsNil(ctx, err4, "更新广告主项目同步时间失败")
		}
	})
	return
}

func (s *sAdProject) AdProjectStatusUpdate(ctx context.Context, req *model.AdProjectStatusUpdateReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		var res = make([]*model.AdProjectStatusUpdateRes, 0)
		err1 := dao.AdProjectAnalytic.Ctx(ctx).As("ap").
			LeftJoin("ad_majordomo_advertiser_account", "ama", "ap.majordomo_advertiser_account_id = ama.id").
			WhereIn("ap.project_id", req.ProjectIds).
			Fields("ap.advertiser_id as advertiserId").
			Fields("ama.access_token as accessToken").
			Fields("GROUP_CONCAT(ap.project_id) as projectIds").
			Group("ap.advertiser_id, ama.access_token").
			Scan(&res)
		liberr.ErrIsNil(ctx, err1, "查询广告项目失败")
		updateProjectIds := make([]string, 0)
		for _, v := range res {
			data := make([]*models.ProjectStatusUpdateV30RequestDataInner, 0)
			projectIds := strings.Split(v.ProjectIds, ",")
			for _, projectId := range projectIds {
				data = append(data, &models.ProjectStatusUpdateV30RequestDataInner{
					OptStatus: req.OptStatus,
					ProjectId: gconv.Int64(projectId),
				})
			}
			request := models.ProjectStatusUpdateV30Request{
				AdvertiserId: gconv.Int64(v.AdvertiserId),
				Data:         data,
			}
			projectStatusUpdateRes, err2 := advertiser.GetToutiaoApiClient().ProjectStatusUpdateV3ApiService.
				AccessToken(v.AccessToken).ProjectStatusUpdateV30Request(request).Do()
			liberr.ErrIsNil(ctx, err2, "更新广告项目状态失败")
			for _, projectId := range projectStatusUpdateRes.Data.ProjectIds {
				updateProjectIds = append(updateProjectIds, gconv.String(projectId))
			}
			if len(updateProjectIds) == 0 {
				continue
			}
			g.Log().Info(ctx, "更新广告项目状态成功: %+v", updateProjectIds)
			var updateSucRes = make([]*model.AdProjectInfoRes, 0)
			err3 := dao.AdProject.Ctx(ctx).WhereIn(dao.AdProject.Columns().ProjectId, updateProjectIds).Scan(&updateSucRes)
			liberr.ErrIsNil(ctx, err3, "查询广告项目失败")
			for _, v1 := range updateSucRes {
				v1.OptStatus = gconv.String(req.OptStatus)
			}
			_, err4 := dao.AdProject.Ctx(ctx).Save(updateSucRes)
			liberr.ErrIsNil(ctx, err4, "更新广告项目失败")
		}
	})
	return
}
