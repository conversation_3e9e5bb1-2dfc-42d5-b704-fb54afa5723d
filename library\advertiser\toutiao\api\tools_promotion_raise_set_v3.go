/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)
//一键起量使用条件：oCPM计划、非nobid&自动化计划（管家，省心投），仅状态为“投放中”的广告支持“立即生效”
//
//每个起量方案生效时间为6小时，冲突会报错
//
//全量更新。传空则更新为空，此时已预约的方案将被删除，生效中的方案不受影响
// ToolsPromotionRaiseSetV30ApiService ToolsPromotionRaiseSetV30Api service
type ToolsPromotionRaiseSetV30ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ToolsPromotionRaiseSetV30Request
}

func (r *ToolsPromotionRaiseSetV30ApiService) SetCfg(cfg *conf.Configuration) *ToolsPromotionRaiseSetV30ApiService {
	r.cfg = cfg
	return r
}

func (r *ToolsPromotionRaiseSetV30ApiService) ToolsWechatAppletCreateV30Request(toolsWechatAppletCreateV30Request models.ToolsPromotionRaiseSetV30Request) *ToolsPromotionRaiseSetV30ApiService {
	r.Request = &toolsWechatAppletCreateV30Request
	return r
}

func (r *ToolsPromotionRaiseSetV30ApiService) AccessToken(accessToken string) *ToolsPromotionRaiseSetV30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ToolsPromotionRaiseSetV30ApiService) Do() (data *models.ToolsPromotionRaiseSetV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/tools/wechat_applet/create/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ToolsPromotionRaiseSetV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ToolsPromotionRaiseSetV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/tools/wechat_applet/create/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
