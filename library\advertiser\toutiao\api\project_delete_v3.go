/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// ProjectDeleteV3ApiService ProjectDeleteV3Api service
type ProjectDeleteV3ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.ProjectDeleteV30Request
}

func (r *ProjectDeleteV3ApiService) SetCfg(cfg *conf.Configuration) *ProjectDeleteV3ApiService {
	r.cfg = cfg
	return r
}

func (r *ProjectDeleteV3ApiService) ProjectDeleteV30Request(projectDeleteV30Request models.ProjectDeleteV30Request) *ProjectDeleteV3ApiService {
	r.Request = &projectDeleteV30Request
	return r
}

func (r *ProjectDeleteV3ApiService) AccessToken(accessToken string) *ProjectDeleteV3ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *ProjectDeleteV3ApiService) Do() (data *models.ProjectDeleteV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/project/delete/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.ProjectDeleteV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.ProjectDeleteV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/project/delete/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
