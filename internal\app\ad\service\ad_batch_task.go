// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/service/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

type IAdBatchTask interface {
	List(ctx context.Context, req *model.AdBatchTaskSearchReq) (res *model.AdBatchTaskSearchRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdBatchTaskInfoRes, err error)
	GetByTaskId(ctx context.Context, taskId string) (res *model.AdBatchTaskInfoRes, err error)
	Add(ctx context.Context, req *model.AdBatchTaskAddReq) (err error)
	Edit(ctx context.Context, req *model.AdBatchTaskEditReq) (err error)
	EditOptStatus(ctx context.Context, req *model.AdBatchTaskEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
	BatchEditAdvertiser(ctx context.Context, req *model.AdBatchTaskEditAdvertiserReq) (err error)
	BatchUpdateProject(ctx context.Context, req *model.BatchUpdateProjectReq) (err error)
	KsBatchUpdateAccount(ctx context.Context, req *model.KsBatchUpdateAccountReq) (err error)
	KsBatchUpdateCampaign(ctx context.Context, req *model.KsBatchUpdateCampaignReq) (err error)
	KsBatchUpdateUnit(ctx context.Context, req *model.KsBatchUpdateUnitReq) (err error)
}

var localAdBatchTask IAdBatchTask

func AdBatchTask() IAdBatchTask {
	if localAdBatchTask == nil {
		panic("implement not found for interface IAdBatchTask, forgot register?")
	}
	return localAdBatchTask
}

func RegisterAdBatchTask(i IAdBatchTask) {
	localAdBatchTask = i
}
