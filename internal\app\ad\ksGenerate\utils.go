package generate

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	commonConst "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"math"
	"strings"
)

// CalcEstimateUnitNum 计算预估生成广告数
func CalcEstimateUnitNum(estimateInfoMap map[int64]*model.EstimateInfo) (estimateUnitNum int) {
	for _, v := range estimateInfoMap {
		estimateUnitNum += v.UnitNum
	}
	return
}

// CalculateUnitNum 计算广告组数量
func CalculateUnitNum(creativeMaterials []*model.CreativeMaterials, titles []*model.KsAdvertiserCommonAssetTitleInfoRes, titleAllocationMethod commonConst.KsTitleAllocationMethod) int {
	materialCount := len(creativeMaterials)
	titleCount := len(titles)
	if materialCount == 0 {
		return 0
	}
	switch titleAllocationMethod {
	case commonConst.KsTitleAllocationMethodAuto:
		// AUTO模式：广告组数量 = 素材数量
		return materialCount
	case commonConst.KsTitleAllocationMethodTest:
		// TEST模式：广告组数量 = 素材数量 * 文案数量
		return materialCount * titleCount
	default:
		// 默认按AUTO处理
		return materialCount
	}
}

// CalculateCampaignNum 计算广告计划数量
func CalculateCampaignNum(unitNum int, adUnitLimit int) int {
	if unitNum == 0 || adUnitLimit <= 0 {
		return 0
	}
	// 广告计划数 = 广告组数量 / 广告计划内广告组上限（向上取整）
	return int(math.Ceil(float64(unitNum) / float64(adUnitLimit)))
}

const (
	KsCampaignDailyNumKey = "KS:AD:CAMPAIGN:DAILY:NUM:"
	KsUnitDailyNumKey     = "KS:AD:UNIT:DAILY:NUM:"
	KsUnitRoiRatio        = "KS:UNIT:ROI:RATIO"
)

// GetCampaignDailyNum 获取广告计划每日标号
func GetCampaignDailyNum(advertiserId string) int {
	data, _ := commonService.RedisCache().Get(context.Background(), fmt.Sprintf("%s%s", KsCampaignDailyNumKey, advertiserId))
	if !data.IsNil() {
		if v, ok := data.Val().(string); ok {
			return gconv.Int(v)
		}
	}
	return 0
}

// IncrCampaignDailyNum 增加广告计划每日标号 提交审核接口调用
func IncrCampaignDailyNum(advertiserId string, increment int) {
	key := fmt.Sprintf("%s%s", KsCampaignDailyNumKey, advertiserId)
	_, _ = commonService.RedisCache().IncrBy(context.Background(), key, gconv.Int64(increment))
	expires := gtime.Now().EndOfDay().Sub(gtime.Now()).Seconds()
	_, _ = commonService.RedisCache().Expire(context.Background(), key, gconv.Int64(expires))
	return
}

// GetUnitDailyNum 获取广告组每日标号
func GetUnitDailyNum(advertiserId string) int {
	data, _ := commonService.RedisCache().Get(context.Background(), fmt.Sprintf("%s%s", KsUnitDailyNumKey, advertiserId))
	if !data.IsNil() {
		if v, ok := data.Val().(string); ok {
			return gconv.Int(v)
		}
	}
	return 0
}

// IncrUnitDailyNum 增加广告组每日标号 提交审核接口调用
func IncrUnitDailyNum(advertiserId string, increment int) {
	key := fmt.Sprintf("%s%s", KsUnitDailyNumKey, advertiserId)
	_, _ = commonService.RedisCache().IncrBy(context.Background(), key, gconv.Int64(increment))
	expires := gtime.Now().EndOfDay().Sub(gtime.Now()).Seconds()
	_, _ = commonService.RedisCache().Expire(context.Background(), key, gconv.Int64(expires))
	return
}

// GetKsUnitRoiRatioByUserId 获取上次的ROI系数
func GetKsUnitRoiRatioByUserId(userId uint64, ocpxActionType int) float64 {
	data, _ := commonService.RedisCache().Get(context.Background(), fmt.Sprintf("%s:%v:%v", KsUnitRoiRatio, userId, ocpxActionType))
	if !data.IsNil() {
		if v, ok := data.Val().(string); ok {
			return gconv.Float64(v)
		}
	}
	return 0
}

// SetKsUnitRoiRatioByUserId 保存ROI系数
func SetKsUnitRoiRatioByUserId(userId uint64, ocpxActionType int, roiRatio float64) {
	_, _ = commonService.RedisCache().Set(context.Background(), fmt.Sprintf("%s:%v:%v", KsUnitRoiRatio, userId, ocpxActionType), roiRatio)
}

// 通配符
const (
	AdvertiserName   = "<账户名称>"
	AdvertiserRemark = "<账户备注>"
	Date             = "<日期>"
	Time             = "<时分秒>"
	DailyNum         = "<动态标号>"
)

// ReplaceWildcards 替换通配符
func ReplaceWildcards(originName string, wildcardsData map[string]string) string {
	for key, value := range wildcardsData {
		originName = strings.ReplaceAll(originName, key, value)
	}
	return originName
}
