// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/model/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
	ksApi "github.com/tiger1103/gfast/v3/library/advertiser/ks/api"
)

// AdBatchTaskInfoRes is the golang structure for table ad_batch_task.
type AdBatchTaskInfoRes struct {
	gmeta.Meta `orm:"table:ad_batch_task"`
	Id         int64       `orm:"id,primary" json:"id" dc:""`                                          //
	TaskId     string      `orm:"task_id" json:"taskId" dc:"任务ID"`                                     // 任务ID
	TaskName   string      `orm:"task_name" json:"taskName" dc:"任务名称"`                                 // 任务名称
	MediaType  int         `orm:"media_type" json:"mediaType" dc:"媒体类型 1：巨量 "`                         // 媒体类型 1：巨量
	OptObject  int         `orm:"opt_object" json:"optObject" dc:"操作对象类型 1：账户 2：项目 3：广告"`              // 操作对象类型 1：账户 2：项目 3：广告
	OptType    int         `orm:"opt_type" json:"optType" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`        // 操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像
	OptNum     int         `orm:"opt_num" json:"optNum" dc:"操作数量"`                                     // 操作数量
	OptStatus  string      `orm:"opt_status" json:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"` // 执行状态：EXECUTING：执行中  COMPLETED：执行完成
	SuccessNum int         `orm:"success_num" json:"successNum" dc:"成功数量"`                             // 成功数量
	FailNum    int         `orm:"fail_num" json:"failNum" dc:"失败数量"`                                   // 失败数量
	UserId     int         `orm:"user_id" json:"userId" dc:"归属人员"`                                     // 归属人员
	CreatedAt  *gtime.Time `orm:"created_at" json:"createdAt" dc:"创建时间"`                               // 创建时间
	UpdatedAt  *gtime.Time `orm:"updated_at" json:"updatedAt" dc:"更新时间"`                               // 更新时间
	DeletedAt  *gtime.Time `orm:"deleted_at" json:"deletedAt" dc:"删除时间"`                               // 删除时间
}

type AdBatchTaskListRes struct {
	Id            int64       `json:"id" dc:""`
	TaskId        string      `json:"taskId" dc:"任务ID"`
	TaskName      string      `json:"taskName" dc:"任务名称"`
	MediaType     int         `json:"mediaType" dc:"媒体类型 1：巨量 "`
	MediaTypeName string      `json:"mediaTypeName" dc:"媒体类型名称"`
	OptObject     int         `json:"optObject" dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptObjectName string      `json:"optObjectName" dc:"操作对象类型名称"`
	OptType       int         `json:"optType" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptTypeName   string      `json:"optTypeName" dc:"操作类型名称"`
	OptNum        int         `json:"optNum" dc:"操作数量"`
	OptStatus     string      `json:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	OptStatusName string      `json:"optStatusName" dc:"执行状态名称"`
	SuccessNum    int         `json:"successNum" dc:"成功数量"`
	FailNum       int         `json:"failNum" dc:"失败数量"`
	UserId        int         `json:"userId" dc:"归属人员"`
	UserName      string      `json:"userName" dc:"归属人员名称"`
	CreatedAt     *gtime.Time `json:"createdAt" dc:"创建时间"`
}

// AdBatchTaskSearchReq 分页请求参数
type AdBatchTaskSearchReq struct {
	comModel.PageReq
	MediaType string `p:"mediaType" v:"mediaType@integer#媒体类型 1：巨量 需为整数" dc:"媒体类型 1：巨量 "`
	OptObject string `p:"optObject" v:"optObject@integer#操作对象类型 1：账户 2：项目 3：广告需为整数" dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType   string `p:"optType" v:"optType@integer#操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像需为整数" dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptStatus string `p:"optStatus" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	StartTime string `p:"startTime" dc:"开始时间"`
	EndTime   string `p:"endTime" dc:"结束时间"`
}

// AdBatchTaskSearchRes 列表返回结果
type AdBatchTaskSearchRes struct {
	comModel.ListRes
	List []*AdBatchTaskListRes `json:"list"`
}

// AdBatchTaskAddReq 添加操作请求参数
type AdBatchTaskAddReq struct {
	TaskId     string `p:"taskId"  dc:"任务ID"`
	TaskName   string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	MediaType  int    `p:"mediaType"  dc:"媒体类型 1：巨量 "`
	OptObject  int    `p:"optObject"  dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType    int    `p:"optType"  dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptNum     int    `p:"optNum"  dc:"操作数量"`
	OptStatus  string `p:"optStatus" v:"required#执行状态：EXECUTING：执行中  COMPLETED：执行完成不能为空" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	SuccessNum int    `p:"successNum"  dc:"成功数量"`
	FailNum    int    `p:"failNum"  dc:"失败数量"`
	UserId     uint64 `p:"userId"  dc:"归属人员"`
}

// AdBatchTaskEditReq 修改操作请求参数
type AdBatchTaskEditReq struct {
	Id         int64  `p:"id" v:"required#主键ID不能为空" dc:""`
	TaskId     string `p:"taskId"  dc:"任务ID"`
	TaskName   string `p:"taskName" v:"required#任务名称不能为空" dc:"任务名称"`
	MediaType  int    `p:"mediaType"  dc:"媒体类型 1：巨量 "`
	OptObject  int    `p:"optObject"  dc:"操作对象类型 1：账户 2：项目 3：广告"`
	OptType    int    `p:"optType"  dc:"操作类型 1：修改账户名称 2：修改账户备注 3：修改账户头像"`
	OptNum     int    `p:"optNum"  dc:"操作数量"`
	OptStatus  string `p:"optStatus" v:"required#执行状态：EXECUTING：执行中  COMPLETED：执行完成不能为空" dc:"执行状态：EXECUTING：执行中  COMPLETED：执行完成"`
	SuccessNum int    `p:"successNum"  dc:"成功数量"`
	FailNum    int    `p:"failNum"  dc:"失败数量"`
	UserId     int    `p:"userId"  dc:"归属人员"`
}

type AdBatchTaskEditAdvertiserReq struct {
	OptType        int                       `p:"optType"  dc:"操作类型 1: 修改账户名称 2: 修改账户备注 3: 修改账户头像"`
	AdvertiserList []BatchEditAdvertiserInfo `p:"advertiserList" dc:"账户信息"`
	File           *ghttp.UploadFile         `p:"file" type:"file" dc:"选择上传头像文件"`
}

type BatchEditAdvertiserInfo struct {
	AdvertiserId             string `p:"advertiserId" dc:"账户ID"`
	OriginalAdvertiserName   string `p:"originalAdvertiserName" dc:"原账户名称"`
	NewAdvertiserName        string `p:"newAdvertiserName" dc:"新账户名称"`
	OriginalAdvertiserRemark string `p:"originalAdvertiserRemark" dc:"原账户备注"`
	NewAdvertiserRemark      string `p:"newAdvertiserRemark" dc:"新账户备注"`
}

type BatchUpdateProjectReq struct {
	OptType            int                      `p:"optType"  dc:"操作类型 14: 启用 8: 暂停 9: 删除 15: 修改出价 11: 修改ROI系数 16: 修改预算 17: 修改项目排期"`
	UpdateProjectInfos []BatchUpdateProjectInfo `p:"updateProjectInfos" dc:"项目信息"`
	ModifyRules        int                      `p:"modifyRules" dc:"修改规则 1: 立即生效 2: 次日0时生效"`
	ScheduleTime       string                   `p:"scheduleTime" dc:"投放时段"`
}

type BatchUpdateProjectInfo struct {
	AdvertiserId string  `p:"advertiserId" dc:"账户ID"`
	ProjectId    string  `p:"projectId" dc:"项目ID"`
	Bid          float64 `p:"bid" dc:"出价"`
	RoiGoal      float64 `p:"roiGoal" dc:"ROI系数"`
	Budget       float64 `p:"budget" dc:"预算"`
}

// BatchUpdateProjectTaskInfo 批量修改巨量项目任务信息
type BatchUpdateProjectTaskInfo struct {
	TaskId       string  `json:"taskId"  dc:"任务ID"`
	SerialNumber int     `json:"serialNumber"  dc:"序号 任务中的执行顺序"`
	OptType      int     `json:"optType"  dc:"操作类型 14: 启用 8: 暂停 9: 删除 15: 修改出价 11: 修改ROI系数 16: 修改预算 17: 修改项目排期"`
	AdvertiserId string  `json:"advertiserId" dc:"账户ID"`
	ProjectId    string  `json:"projectId" dc:"项目ID"`
	CpaBid       float64 `json:"cpaBid" dc:"出价"`
	RoiGoal      float64 `json:"roiGoal" dc:"ROI系数"`
	Budget       float64 `json:"budget" dc:"预算"`
	ScheduleTime string  `json:"scheduleTime" dc:"投放时段"`
}

type KsBatchUpdateAccountReq struct {
	OptType                  int                              `p:"optType"  dc:"操作类型 4: 修改账户预算 5: 修改账户智投 6: 修改增量探索"`
	AdvertiserIds            []int64                          `p:"advertiserIds" dc:"账户ID列表"`
	DayBudget                int64                            `p:"day_budget" dc:"账户预算 不限传0"`
	AccountAutoManage        int                              `p:"account_auto_manage" dc:"账户智投开关 1：开启 0：关闭"`
	OcpxActionTypeConstraint []ksApi.OcpxActionTypeConstraint `p:"ocpx_action_type_constraint" dc:"账户智投目标成本配置"`
	IncExploreInfo           []ksApi.GwIncExploreDetailDto    `p:"inc_explore_info" dc:"增量探索配置列表"`
}

// KsBatchUpdateAccountTaskInfo 批量修改账户任务信息
type KsBatchUpdateAccountTaskInfo struct {
	TaskId                   string                           `json:"taskId"  dc:"任务ID"`
	SerialNumber             int                              `json:"serialNumber"  dc:"序号 任务中的执行顺序"`
	OptType                  int                              `json:"optType"  dc:"操作类型 4: 修改账户预算 5: 修改账户智投 6: 修改增量探索"`
	AdvertiserId             int64                            `json:"advertiserId" dc:"账户ID"`
	DayBudget                int64                            `json:"dayBudget" dc:"账户预算 不限传0"`
	AccountAutoManage        int                              `json:"account_auto_manage" dc:"账户智投开关"`
	OcpxActionTypeConstraint []ksApi.OcpxActionTypeConstraint `json:"ocpx_action_type_constraint" dc:"账户智投目标成本配置"`
	IncExploreInfo           []ksApi.GwIncExploreDetailDto    `json:"inc_explore_info" dc:"增量探索配置列表"`
}

type KsBatchUpdateCampaignReq struct {
	OptType             int                       `p:"optType"  dc:"操作类型 7: 投放 8: 暂停 9: 删除 10: 修改日预算"`
	UpdateCampaignInfos []BatchUpdateCampaignInfo `p:"updateCampaignInfos" dc:"广告计划信息"`
	DayBudget           int64                     `p:"dayBudget" dc:"预算 不限传0"`
}

type BatchUpdateCampaignInfo struct {
	AdvertiserId int64 `p:"advertiserId" dc:"账户ID"`
	CampaignId   int64 `p:"campaignId" dc:"广告计划ID"`
}

// KsBatchUpdateCampaignTaskInfo 批量修改广告计划任务信息
type KsBatchUpdateCampaignTaskInfo struct {
	TaskId       string `json:"taskId"  dc:"任务ID"`
	SerialNumber int    `json:"serialNumber"  dc:"序号 任务中的执行顺序"`
	OptType      int    `json:"optType"  dc:"操作类型 7: 投放 8: 暂停 9: 删除 10: 修改日预算"`
	AdvertiserId int64  `json:"advertiserId" dc:"账户ID"`
	CampaignId   int64  `json:"campaignId" dc:"广告计划ID"`
	DayBudget    int64  `json:"dayBudget" dc:"预算 不限传0"`
}

type KsBatchUpdateUnitReq struct {
	OptType         int                   `p:"optType"  dc:"操作类型 7: 投放 8: 暂停 9: 删除 11: 修改ROI系数 12: 修改广告组名称 13: 修改投放时段"`
	UpdateUnitInfos []BatchUpdateUnitInfo `p:"updateUnitInfos" dc:"广告组信息"`
}

type BatchUpdateUnitInfo struct {
	AdvertiserId int64   `p:"advertiserId" v:"required#账户ID必须" dc:"账户ID"`
	UnitId       int64   `p:"unitId" v:"required#广告组ID必须" dc:"广告组ID"`
	UnitName     string  `p:"unitName" v:"required#广告组名称必须" dc:"广告组名称"`
	NewUnitName  string  `p:"newUnitName" dc:"新广告组名称"`
	RoiRatio     float64 `p:"roiRatio" dc:"ROI系数"`
	BeginTime    string  `p:"beginTime" dc:"投放开始时间"`
	EndTime      string  `p:"endTime" dc:"投放结束时间"`
	ScheduleTime string  `p:"scheduleTime" dc:"投放时段"`
}

// KsBatchUpdateUnitTaskInfo 批量修改广告组任务信息
type KsBatchUpdateUnitTaskInfo struct {
	TaskId       string  `json:"taskId"  dc:"任务ID"`
	SerialNumber int     `json:"serialNumber"  dc:"序号 任务中的执行顺序"`
	OptType      int     `json:"optType"  dc:"操作类型 7: 投放 8: 暂停 9: 删除 11: 修改ROI系数 12: 修改广告组名称 13: 修改投放时段"`
	AdvertiserId int64   `json:"advertiserId" dc:"账户ID"`
	UnitId       int64   `json:"unitId" dc:"广告组ID"`
	UnitName     string  `json:"unitName" dc:"广告组名称"`
	NewUnitName  string  `json:"newUnitName" dc:"新广告组名称"`
	RoiRatio     float64 `json:"roiRatio" dc:"ROI系数"`
	BeginTime    string  `json:"beginTime" dc:"投放开始时间"`
	EndTime      string  `json:"endTime" dc:"投放结束时间"`
	ScheduleTime string  `json:"scheduleTime" dc:"投放时段"`
}
