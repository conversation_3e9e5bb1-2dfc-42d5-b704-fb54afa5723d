// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-03-27 17:29:59
// 生成路径: internal/app/ad/controller/ad_batch_task.go
// 生成人：cq
// desc:广告批量操作任务
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adBatchTaskController struct {
	systemController.BaseController
}

var AdBatchTask = new(adBatchTaskController)

// List 列表
func (c *adBatchTaskController) List(ctx context.Context, req *ad.AdBatchTaskSearchReq) (res *ad.AdBatchTaskSearchRes, err error) {
	res = new(ad.AdBatchTaskSearchRes)
	res.AdBatchTaskSearchRes, err = service.AdBatchTask().List(ctx, &req.AdBatchTaskSearchReq)
	return
}

// Get 获取广告批量操作任务
func (c *adBatchTaskController) Get(ctx context.Context, req *ad.AdBatchTaskGetReq) (res *ad.AdBatchTaskGetRes, err error) {
	res = new(ad.AdBatchTaskGetRes)
	res.AdBatchTaskInfoRes, err = service.AdBatchTask().GetById(ctx, req.Id)
	return
}

// Add 添加广告批量操作任务
func (c *adBatchTaskController) Add(ctx context.Context, req *ad.AdBatchTaskAddReq) (res *ad.AdBatchTaskAddRes, err error) {
	err = service.AdBatchTask().Add(ctx, req.AdBatchTaskAddReq)
	return
}

// Edit 修改广告批量操作任务
func (c *adBatchTaskController) Edit(ctx context.Context, req *ad.AdBatchTaskEditReq) (res *ad.AdBatchTaskEditRes, err error) {
	err = service.AdBatchTask().Edit(ctx, req.AdBatchTaskEditReq)
	return
}

// Delete 删除广告批量操作任务
func (c *adBatchTaskController) Delete(ctx context.Context, req *ad.AdBatchTaskDeleteReq) (res *ad.AdBatchTaskDeleteRes, err error) {
	err = service.AdBatchTask().Delete(ctx, req.Ids)
	return
}

// BatchEditAdvertiser 批量修改账户信息
func (c *adBatchTaskController) BatchEditAdvertiser(ctx context.Context, req *ad.AdBatchTaskEditAdvertiserReq) (res *ad.AdBatchTaskEditAdvertiserRes, err error) {
	err = service.AdBatchTask().BatchEditAdvertiser(ctx, &req.AdBatchTaskEditAdvertiserReq)
	return
}

// BatchUpdateProject 批量修改项目信息
func (c *adBatchTaskController) BatchUpdateProject(ctx context.Context, req *ad.AdBatchTaskBatchUpdateProjectReq) (res *ad.AdBatchTaskBatchUpdateProjectRes, err error) {
	err = service.AdBatchTask().BatchUpdateProject(ctx, &req.BatchUpdateProjectReq)
	return
}

// KsBatchUpdateAccount 批量修改快手账户信息
func (c *adBatchTaskController) KsBatchUpdateAccount(ctx context.Context, req *ad.AdBatchTaskKsBatchUpdateAccountReq) (res *ad.AdBatchTaskKsBatchUpdateAccountRes, err error) {
	err = service.AdBatchTask().KsBatchUpdateAccount(ctx, &req.KsBatchUpdateAccountReq)
	return
}

// KsBatchUpdateCampaign 批量修改快手广告计划信息
func (c *adBatchTaskController) KsBatchUpdateCampaign(ctx context.Context, req *ad.AdBatchTaskKsBatchUpdateCampaignReq) (res *ad.AdBatchTaskKsBatchUpdateCampaignRes, err error) {
	err = service.AdBatchTask().KsBatchUpdateCampaign(ctx, &req.KsBatchUpdateCampaignReq)
	return
}

// KsBatchUpdateUnit 批量修改快手广告组信息
func (c *adBatchTaskController) KsBatchUpdateUnit(ctx context.Context, req *ad.AdBatchTaskKsBatchUpdateUnitReq) (res *ad.AdBatchTaskKsBatchUpdateUnitRes, err error) {
	err = service.AdBatchTask().KsBatchUpdateUnit(ctx, &req.KsBatchUpdateUnitReq)
	return
}
