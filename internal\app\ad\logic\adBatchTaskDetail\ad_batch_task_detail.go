// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/logic/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/dao"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdBatchTaskDetail(New())
}

func New() service.IAdBatchTaskDetail {
	return &sAdBatchTaskDetail{}
}

type sAdBatchTaskDetail struct{}

func (s *sAdBatchTaskDetail) List(ctx context.Context, req *model.AdBatchTaskDetailSearchReq) (listRes *model.AdBatchTaskDetailSearchRes, err error) {
	listRes = new(model.AdBatchTaskDetailSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		m := dao.AdBatchTaskDetail.Ctx(ctx).WithAll()
		if req.Id != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().Id+" = ?", req.Id)
		}
		if req.TaskId != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().TaskId+" = ?", req.TaskId)
		}
		if req.AdvertiserId != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().AdvertiserId+" = ?", req.AdvertiserId)
		}
		if req.ProjectId != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().ProjectId+" = ?", req.ProjectId)
		}
		if req.PromotionId != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().PromotionId+" = ?", req.PromotionId)
		}
		if req.OptResult != "" {
			m = m.Where(dao.AdBatchTaskDetail.Columns().OptResult+" = ?", req.OptResult)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "serial_number asc, id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		var res []*model.AdBatchTaskDetailListRes
		err = m.Page(req.PageNum, req.PageSize).Order(order).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
		listRes.List = make([]*model.AdBatchTaskDetailListRes, len(res))
		for k, v := range res {
			listRes.List[k] = &model.AdBatchTaskDetailListRes{
				Id:                       v.Id,
				TaskId:                   v.TaskId,
				SerialNumber:             v.SerialNumber,
				AdvertiserId:             v.AdvertiserId,
				ProjectId:                v.ProjectId,
				PromotionId:              v.PromotionId,
				CampaignId:               v.CampaignId,
				UnitId:                   v.UnitId,
				OriginalUnitName:         v.OriginalUnitName,
				NewUnitName:              v.NewUnitName,
				OriginalAdvertiserName:   v.OriginalAdvertiserName,
				NewAdvertiserName:        v.NewAdvertiserName,
				OriginalAdvertiserRemark: v.OriginalAdvertiserRemark,
				NewAdvertiserRemark:      v.NewAdvertiserRemark,
				OptResult:                v.OptResult,
				OptResultName:            commonConsts.OptResultMapping[v.OptResult],
				ErrMsg:                   v.ErrMsg,
				CreatedAt:                v.CreatedAt,
			}
		}
	})
	return
}

func (s *sAdBatchTaskDetail) GetById(ctx context.Context, id int64) (res *model.AdBatchTaskDetailInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdBatchTaskDetail.Ctx(ctx).WithAll().Where(dao.AdBatchTaskDetail.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdBatchTaskDetail) Add(ctx context.Context, req *model.AdBatchTaskDetailAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdBatchTaskDetail.Ctx(ctx).Insert(do.AdBatchTaskDetail{
			TaskId:                   req.TaskId,
			SerialNumber:             req.SerialNumber,
			AdvertiserId:             req.AdvertiserId,
			ProjectId:                req.ProjectId,
			PromotionId:              req.PromotionId,
			CampaignId:               req.CampaignId,
			UnitId:                   req.UnitId,
			OriginalUnitName:         req.OriginalUnitName,
			NewUnitName:              req.NewUnitName,
			OriginalAdvertiserName:   req.OriginalAdvertiserName,
			NewAdvertiserName:        req.NewAdvertiserName,
			OriginalAdvertiserRemark: req.OriginalAdvertiserRemark,
			NewAdvertiserRemark:      req.NewAdvertiserRemark,
			OptResult:                req.OptResult,
			ErrMsg:                   req.ErrMsg,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdBatchTaskDetail) BatchAdd(ctx context.Context, batchReq []*model.AdBatchTaskDetailAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		data := make([]*do.AdBatchTaskDetail, len(batchReq))
		for k, v := range batchReq {
			data[k] = &do.AdBatchTaskDetail{
				TaskId:                   v.TaskId,
				SerialNumber:             v.SerialNumber,
				AdvertiserId:             v.AdvertiserId,
				ProjectId:                v.ProjectId,
				PromotionId:              v.PromotionId,
				CampaignId:               v.CampaignId,
				UnitId:                   v.UnitId,
				OriginalUnitName:         v.OriginalUnitName,
				NewUnitName:              v.NewUnitName,
				OriginalAdvertiserName:   v.OriginalAdvertiserName,
				NewAdvertiserName:        v.NewAdvertiserName,
				OriginalAdvertiserRemark: v.OriginalAdvertiserRemark,
				NewAdvertiserRemark:      v.NewAdvertiserRemark,
				OptResult:                v.OptResult,
				ErrMsg:                   v.ErrMsg,
			}
		}
		_, err = dao.AdBatchTaskDetail.Ctx(ctx).Save(data)
	})
	return
}

func (s *sAdBatchTaskDetail) Edit(ctx context.Context, req *model.AdBatchTaskDetailEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdBatchTaskDetail.Ctx(ctx).WherePri(req.Id).Update(do.AdBatchTaskDetail{
			TaskId:                   req.TaskId,
			SerialNumber:             req.SerialNumber,
			AdvertiserId:             req.AdvertiserId,
			ProjectId:                req.ProjectId,
			PromotionId:              req.PromotionId,
			CampaignId:               req.CampaignId,
			UnitId:                   req.UnitId,
			OriginalUnitName:         req.OriginalUnitName,
			NewUnitName:              req.NewUnitName,
			OriginalAdvertiserName:   req.OriginalAdvertiserName,
			NewAdvertiserName:        req.NewAdvertiserName,
			OriginalAdvertiserRemark: req.OriginalAdvertiserRemark,
			NewAdvertiserRemark:      req.NewAdvertiserRemark,
			OptResult:                req.OptResult,
			ErrMsg:                   req.ErrMsg,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdBatchTaskDetail) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdBatchTaskDetail.Ctx(ctx).Delete(dao.AdBatchTaskDetail.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
