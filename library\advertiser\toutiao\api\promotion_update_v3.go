/*
Oceanengine Open Api

巨量引擎开放平台 Open Api


*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package api

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/conf"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
)

// PromotionUpdateV30ApiService PromotionUpdateV30Api service 修改广告 先查一遍再进行修改
type PromotionUpdateV30ApiService struct {
	ctx     context.Context
	cfg     *conf.Configuration
	token   string
	Request *models.PromotionUpdateV30Request
}

func (r *PromotionUpdateV30ApiService) SetCfg(cfg *conf.Configuration) *PromotionUpdateV30ApiService {
	r.cfg = cfg
	return r
}

func (r *PromotionUpdateV30ApiService) PromotionStatusUpdateV30Request(promotionStatusUpdateV30Request models.PromotionUpdateV30Request) *PromotionUpdateV30ApiService {
	r.Request = &promotionStatusUpdateV30Request
	return r
}

func (r *PromotionUpdateV30ApiService) AccessToken(accessToken string) *PromotionUpdateV30ApiService {
	r.token = accessToken
	return r
}

// Do 执行HttpClient请求 外层无需关注是get还是post
func (r *PromotionUpdateV30ApiService) Do() (data *models.PromotionUpdateV30Response, err error) {
	localBasePath := r.cfg.GetBasePath()
	localVarPath := localBasePath + "/open_api/v3.0/promotion/update/"
	response, err := r.cfg.HTTPClient.R().
		SetHeader("Content-Type", "application/json").
		SetHeader("Access-Token", r.token).
		SetBody(r.Request).
		SetResult(&models.PromotionUpdateV30Response{}).
		Post(localVarPath)
	if err != nil {
		return nil, err
	}
	resp := new(models.PromotionUpdateV30Response)
	// 将 JSON 响应解码到结构体中
	err = json.Unmarshal(response.Body(), &resp)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("/open_api/v3.0/promotion/update/解析响应出错: %v\n", err))
	}
	if *resp.Code == 0 && resp.Data != nil {
		return resp, nil
	} else {
		return resp, errors.New(*resp.Message)
	}
}
