// ==========================================================================
// GFast自动生成api操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: api/v1/ad/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务相关参数
// company:云南奇讯科技有限公司
// ==========================================================================

package ad

import (
	"github.com/gogf/gf/v2/frame/g"
	commonApi "github.com/tiger1103/gfast/v3/api/v1/common"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
)

// KsAdvertiserStrategyTaskSearchReq 分页请求参数
type KsAdvertiserStrategyTaskSearchReq struct {
	g.Meta `path:"/list" tags:"快手广告搭建-任务" method:"get" summary:"快手广告搭建-任务列表"`
	commonApi.Author
	model.KsAdvertiserStrategyTaskSearchReq
}

// KsAdvertiserStrategyTaskSearchRes 列表返回结果
type KsAdvertiserStrategyTaskSearchRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskSearchRes
}

// KsAdvertiserStrategyTaskAddReq 添加操作请求参数
type KsAdvertiserStrategyTaskAddReq struct {
	g.Meta `path:"/add" tags:"快手广告搭建-任务" method:"post" summary:"快手广告搭建-任务添加"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskAddReq
}

// KsAdvertiserStrategyTaskAddRes 添加操作返回结果
type KsAdvertiserStrategyTaskAddRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskEditReq 修改操作请求参数
type KsAdvertiserStrategyTaskEditReq struct {
	g.Meta `path:"/edit" tags:"快手广告搭建-任务" method:"put" summary:"快手广告搭建-任务修改"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskEditReq
}

// KsAdvertiserStrategyTaskEditRes 修改操作返回结果
type KsAdvertiserStrategyTaskEditRes struct {
	commonApi.EmptyRes
}

// 修改任务名称
type KsAdvertiserStrategyTaskUpdateNameReq struct {
	g.Meta `path:"/updateName" tags:"快手广告搭建-任务" method:"put" summary:"修改任务名称"`
	commonApi.Author
	*model.KsAdvertiserStrategyTaskUpdateNameReq
}
type KsAdvertiserStrategyTaskUpdateNameRes struct {
	commonApi.EmptyRes
}

// KsAdvertiserStrategyTaskGetReq 获取一条数据请求
type KsAdvertiserStrategyTaskGetReq struct {
	g.Meta `path:"/get" tags:"快手广告搭建-任务" method:"get" summary:"获取快手广告搭建-任务信息"`
	commonApi.Author
	TaskId string `p:"taskId" v:"required#主键必须"` //通过主键获取
}

// KsAdvertiserStrategyTaskGetRes 获取一条数据结果
type KsAdvertiserStrategyTaskGetRes struct {
	g.Meta `mime:"application/json"`
	*model.KsAdvertiserStrategyTaskInfoRes
}

// KsAdvertiserStrategyTaskDeleteReq 删除数据请求
type KsAdvertiserStrategyTaskDeleteReq struct {
	g.Meta `path:"/delete" tags:"快手广告搭建-任务" method:"delete" summary:"删除快手广告搭建-任务"`
	commonApi.Author
	TaskIds []string `p:"taskIds" v:"required#主键必须"` //通过主键删除
}

// KsAdvertiserStrategyTaskDeleteRes 删除数据返回
type KsAdvertiserStrategyTaskDeleteRes struct {
	commonApi.EmptyRes
}
