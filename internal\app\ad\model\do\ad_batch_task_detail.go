// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-03-27 17:30:31
// 生成路径: internal/app/ad/model/entity/ad_batch_task_detail.go
// 生成人：cq
// desc:广告批量操作任务详情
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdBatchTaskDetail is the golang structure for table ad_batch_task_detail.
type AdBatchTaskDetail struct {
	gmeta.Meta               `orm:"table:ad_batch_task_detail, do:true"`
	Id                       interface{} `orm:"id,primary" json:"id"`                                       //
	TaskId                   interface{} `orm:"task_id" json:"taskId"`                                      // 任务ID
	SerialNumber             interface{} `orm:"serial_number" json:"serialNumber"`                          // 序号 任务中的执行顺序
	AdvertiserId             interface{} `orm:"advertiser_id" json:"advertiserId"`                          // 媒体账户ID
	ProjectId                interface{} `orm:"project_id" json:"projectId"`                                // 项目ID
	PromotionId              interface{} `orm:"promotion_id" json:"promotionId"`                            // 广告ID
	CampaignId               interface{} `orm:"campaign_id" json:"campaignId"`                              // 广告计划ID
	UnitId                   interface{} `orm:"unit_id" json:"unitId"`                                      // 广告组ID
	OriginalUnitName         interface{} `orm:"original_unit_name" json:"originalUnitName"`                 // 原广告组名称
	NewUnitName              interface{} `orm:"new_unit_name" json:"newUnitName"`                           // 新广告组名称
	OriginalAdvertiserName   interface{} `orm:"original_advertiser_name" json:"originalAdvertiserName"`     // 原账户名称
	NewAdvertiserName        interface{} `orm:"new_advertiser_name" json:"newAdvertiserName"`               // 新账户名称
	OriginalAdvertiserRemark interface{} `orm:"original_advertiser_remark" json:"originalAdvertiserRemark"` // 原账户备注
	NewAdvertiserRemark      interface{} `orm:"new_advertiser_remark" json:"newAdvertiserRemark"`           // 新账户备注
	OptResult                interface{} `orm:"opt_result" json:"optResult"`                                // 执行结果：SUCCESS：成功  FAIL：失败
	ErrMsg                   interface{} `orm:"err_msg" json:"errMsg"`                                      // 失败原因
	CreatedAt                *gtime.Time `orm:"created_at" json:"createdAt"`                                // 创建时间
	UpdatedAt                *gtime.Time `orm:"updated_at" json:"updatedAt"`                                // 更新时间
	DeletedAt                *gtime.Time `orm:"deleted_at" json:"deletedAt"`                                // 删除时间
}
