// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2024-11-16 10:32:43
// 生成路径: internal/app/oceanengine/service/ad_project.go
// 生成人：cq
// desc:巨量项目表
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdProject interface {
	List(ctx context.Context, req *model.AdProjectSearchReq) (res *model.AdProjectSearchRes, err error)
	GetTaskList(ctx context.Context, req *model.AdAdvertiserTaskSearchReq) (listRes *model.AdAdvertiserProjectTaskSearchRes, err error)
	GetExportData(ctx context.Context, req *model.AdProjectSearchReq) (listRes []*model.AdProjectInfoRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdProjectInfoRes, err error)
	GetByProjectId(ctx context.Context, projectId string) (res *model.AdProjectInfoRes, err error)
	GetByProjectIds(ctx context.Context, projectIds []string) (res []*model.AdProjectInfoRes, err error)
	GetMapByProjectIds(ctx context.Context, projectIds []string) (res map[string][]*model.AdProjectInfoRes, err error)
	GetProjectNumByAdvertiserId(ctx context.Context, advertiserId string) (count int, err error)
	GetProjectBudget(ctx context.Context, projectIds []string) (budgetMap map[string]float64, err error)
	GetAdProjectName(ctx context.Context, projectIds []string) (nameMap map[string]string, err error)
	Add(ctx context.Context, req *model.AdProjectAddReq) (err error)
	BatchAdd(ctx context.Context, req []*model.AdProjectAddReq) (err error)
	Edit(ctx context.Context, req *model.AdProjectEditReq) (err error)
	UpdateOptStatus(ctx context.Context, projectId string, optStatus string) (err error)
	UpdateCpaBid(ctx context.Context, projectId string, cpaBid float64) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
	DeleteByProjectId(ctx context.Context, projectId string) (err error)
	RunSyncAdProject(ctx context.Context, req *model.SyncAdProjectReq) (err error)
	SyncAdProject(ctx context.Context, statDate string, advertiserIds []string, projectIds []int64) (tokenMap map[string]string, err error)
	AdProjectStatusUpdate(ctx context.Context, req *model.AdProjectStatusUpdateReq) (err error)
}

var localAdProject IAdProject

func AdProject() IAdProject {
	if localAdProject == nil {
		panic("implement not found for interface IAdProject, forgot register?")
	}
	return localAdProject
}

func RegisterAdProject(i IAdProject) {
	localAdProject = i
}
