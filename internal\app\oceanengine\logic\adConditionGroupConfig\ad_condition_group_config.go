// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/logic/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	systemModel "github.com/tiger1103/gfast/v3/internal/app/system/model"
	sysService "github.com/tiger1103/gfast/v3/internal/app/system/service"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model/do"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/internal/app/system/consts"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

func init() {
	service.RegisterAdConditionGroupConfig(New())
}

func New() service.IAdConditionGroupConfig {
	return &sAdConditionGroupConfig{}
}

type sAdConditionGroupConfig struct{}

func (s *sAdConditionGroupConfig) List(ctx context.Context, req *model.AdConditionGroupConfigSearchReq) (listRes *model.AdConditionGroupConfigSearchRes, err error) {
	listRes = new(model.AdConditionGroupConfigSearchRes)
	err = g.Try(ctx, func(ctx context.Context) {
		userInfo := sysService.Context().GetLoginUser(ctx)
		userIds, admin, _ := sysService.SysUser().GetContainUser(ctx, &systemModel.ContextUser{
			LoginUserRes: &systemModel.LoginUserRes{
				Id:     userInfo.Id,
				DeptId: userInfo.DeptId,
			},
		})
		m := dao.AdConditionGroupConfig.Ctx(ctx).WithAll().As("ad").
			LeftJoin("sys_user u", "ad.user_id = u.id")
		if !admin && len(userIds) > 0 {
			m = m.WhereIn("ad."+dao.AdConditionGroupConfig.Columns().UserId, userIds)
		}
		if req.Name != "" {
			m = m.Where("ad."+dao.AdConditionGroupConfig.Columns().Name+" like ?", "%"+req.Name+"%")
		}
		if req.OptType != "" {
			m = m.Where("ad."+dao.AdConditionGroupConfig.Columns().OptType+" = ?", req.OptType)
		}
		if req.Modules != "" {
			m = m.Where("ad."+dao.AdConditionGroupConfig.Columns().Modules+" = ?", req.Modules)
		}
		listRes.Total, err = m.Count()
		liberr.ErrIsNil(ctx, err, "获取总行数失败")
		if req.PageNum == 0 {
			req.PageNum = 1
		}
		listRes.CurrentPage = req.PageNum
		if req.PageSize == 0 {
			req.PageSize = consts.PageSize
		}
		order := "id desc"
		if req.OrderBy != "" {
			order = req.OrderBy
		}
		err = m.Fields("ad.id as id").
			Fields("ad.name as name").
			Fields("ad.condition_group as condition_group").
			Fields("ad.opt_type as opt_type").
			Fields("ad.modules as modules").
			Fields("ad.user_id as user_id").
			Fields("ad.created_at as created_at").
			Page(req.PageNum, req.PageSize).Order(order).Scan(&listRes.List)
		liberr.ErrIsNil(ctx, err, "获取数据失败")
	})
	return
}

func (s *sAdConditionGroupConfig) GetById(ctx context.Context, id int64) (res *model.AdConditionGroupConfigInfoRes, err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		err = dao.AdConditionGroupConfig.Ctx(ctx).WithAll().Where(dao.AdConditionGroupConfig.Columns().Id, id).Scan(&res)
		liberr.ErrIsNil(ctx, err, "获取信息失败")
	})
	return
}

func (s *sAdConditionGroupConfig) Add(ctx context.Context, req *model.AdConditionGroupConfigAddReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		userId := sysService.Context().GetUserId(ctx)
		_, err = dao.AdConditionGroupConfig.Ctx(ctx).Insert(do.AdConditionGroupConfig{
			Name:           req.Name,
			ConditionGroup: req.ConditionGroup,
			OptType:        req.OptType,
			Modules:        req.Modules,
			UserId:         userId,
		})
		liberr.ErrIsNil(ctx, err, "添加失败")
	})
	return
}

func (s *sAdConditionGroupConfig) Edit(ctx context.Context, req *model.AdConditionGroupConfigEditReq) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdConditionGroupConfig.Ctx(ctx).WherePri(req.Id).Update(do.AdConditionGroupConfig{
			Name:           req.Name,
			ConditionGroup: req.ConditionGroup,
			OptType:        req.OptType,
			Modules:        req.Modules,
		})
		liberr.ErrIsNil(ctx, err, "修改失败")
	})
	return
}

func (s *sAdConditionGroupConfig) Delete(ctx context.Context, ids []int64) (err error) {
	err = g.Try(ctx, func(ctx context.Context) {
		_, err = dao.AdConditionGroupConfig.Ctx(ctx).Delete(dao.AdConditionGroupConfig.Columns().Id+" in (?)", ids)
		liberr.ErrIsNil(ctx, err, "删除失败")
	})
	return
}
