// ==========================================================================
// GFast自动生成dao internal操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/dao/internal/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// AdConditionGroupConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
type AdConditionGroupConfigDao struct {
	table   string                        // Table is the underlying table name of the DAO.
	group   string                        // Group is the database configuration group name of current DAO.
	columns AdConditionGroupConfigColumns // Columns is the short type for Columns, which contains all the column names of Table for convenient usage.
}

// AdConditionGroupConfigColumns defines and stores column names for table ad_condition_group_config.
type AdConditionGroupConfigColumns struct {
	Id             string //
	Name           string // 组合名称
	ConditionGroup string // 查询条件组合
	OptType        string // 查询条件组合类型 所有：and 任一：or
	Modules        string // 操作模块：项目：PROJECT  广告：PROMOTION
	UserId         string // 用户ID
	CreatedAt      string // 创建时间
	UpdatedAt      string // 更新时间
	DeletedAt      string // 删除时间
}

var adConditionGroupConfigColumns = AdConditionGroupConfigColumns{
	Id:             "id",
	Name:           "name",
	ConditionGroup: "condition_group",
	OptType:        "opt_type",
	Modules:        "modules",
	UserId:         "user_id",
	CreatedAt:      "created_at",
	UpdatedAt:      "updated_at",
	DeletedAt:      "deleted_at",
}

// NewAdConditionGroupConfigDao creates and returns a new DAO object for table data access.
func NewAdConditionGroupConfigDao() *AdConditionGroupConfigDao {
	return &AdConditionGroupConfigDao{
		group:   "default",
		table:   "ad_condition_group_config",
		columns: adConditionGroupConfigColumns,
	}
}

// DB retrieves and returns the underlying raw database management object of current DAO.
func (dao *AdConditionGroupConfigDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of current dao.
func (dao *AdConditionGroupConfigDao) Table() string {
	return dao.table
}

// Columns returns all column names of current dao.
func (dao *AdConditionGroupConfigDao) Columns() AdConditionGroupConfigColumns {
	return dao.columns
}

// Group returns the configuration group name of database of current dao.
func (dao *AdConditionGroupConfigDao) Group() string {
	return dao.group
}

// Ctx creates and returns the Model for current DAO, It automatically sets the context for current operation.
func (dao *AdConditionGroupConfigDao) Ctx(ctx context.Context) *gdb.Model {
	return dao.DB().Model(dao.table).Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rollbacks the transaction and returns the error from function f if it returns non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note that, you should not Commit or Rollback the transaction in function f
// as it is automatically handled by this function.
func (dao *AdConditionGroupConfigDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
