// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-08-23 17:40:11
// 生成路径: internal/app/ad/controller/ks_advertiser_strategy_task.go
// 生成人：cyao
// desc:快手广告搭建-任务
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/ad"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type ksAdvertiserStrategyTaskController struct {
	systemController.BaseController
}

var KsAdvertiserStrategyTask = new(ksAdvertiserStrategyTaskController)

// List 列表
func (c *ksAdvertiserStrategyTaskController) List(ctx context.Context, req *ad.KsAdvertiserStrategyTaskSearchReq) (res *ad.KsAdvertiserStrategyTaskSearchRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskSearchRes)
	res.KsAdvertiserStrategyTaskSearchRes, err = service.KsAdvertiserStrategyTask().List(ctx, &req.KsAdvertiserStrategyTaskSearchReq)
	return
}

// Get 获取快手广告搭建-任务
func (c *ksAdvertiserStrategyTaskController) Get(ctx context.Context, req *ad.KsAdvertiserStrategyTaskGetReq) (res *ad.KsAdvertiserStrategyTaskGetRes, err error) {
	res = new(ad.KsAdvertiserStrategyTaskGetRes)
	res.KsAdvertiserStrategyTaskInfoRes, err = service.KsAdvertiserStrategyTask().GetByTaskId(ctx, req.TaskId)
	return
}

// Add 添加快手广告搭建-任务
func (c *ksAdvertiserStrategyTaskController) Add(ctx context.Context, req *ad.KsAdvertiserStrategyTaskAddReq) (res *ad.KsAdvertiserStrategyTaskAddRes, err error) {
	err = service.KsAdvertiserStrategyTask().Add(ctx, req.KsAdvertiserStrategyTaskAddReq)
	return
}

// Edit 修改快手广告搭建-任务
func (c *ksAdvertiserStrategyTaskController) Edit(ctx context.Context, req *ad.KsAdvertiserStrategyTaskEditReq) (res *ad.KsAdvertiserStrategyTaskEditRes, err error) {
	err = service.KsAdvertiserStrategyTask().Edit(ctx, req.KsAdvertiserStrategyTaskEditReq)
	return
}

// UpdateName
func (c *ksAdvertiserStrategyTaskController) UpdateName(ctx context.Context, req *ad.KsAdvertiserStrategyTaskUpdateNameReq) (res *ad.KsAdvertiserStrategyTaskUpdateNameRes, err error) {
	err = service.KsAdvertiserStrategyTask().UpdateName(ctx, req.TaskId, req.TaskName)
	return

}

// Delete 删除快手广告搭建-任务
func (c *ksAdvertiserStrategyTaskController) Delete(ctx context.Context, req *ad.KsAdvertiserStrategyTaskDeleteReq) (res *ad.KsAdvertiserStrategyTaskDeleteRes, err error) {
	err = service.KsAdvertiserStrategyTask().Delete(ctx, req.TaskIds)
	return
}
