// ==========================================================================
// GFast自动生成controller操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/controller/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package controller

import (
	"context"

	"github.com/tiger1103/gfast/v3/api/v1/oceanengine"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	systemController "github.com/tiger1103/gfast/v3/internal/app/system/controller"
)

type adConditionGroupConfigController struct {
	systemController.BaseController
}

var AdConditionGroupConfig = new(adConditionGroupConfigController)

// List 列表
func (c *adConditionGroupConfigController) List(ctx context.Context, req *oceanengine.AdConditionGroupConfigSearchReq) (res *oceanengine.AdConditionGroupConfigSearchRes, err error) {
	res = new(oceanengine.AdConditionGroupConfigSearchRes)
	res.AdConditionGroupConfigSearchRes, err = service.AdConditionGroupConfig().List(ctx, &req.AdConditionGroupConfigSearchReq)
	return
}

// Get 获取广告筛选条件组合配置
func (c *adConditionGroupConfigController) Get(ctx context.Context, req *oceanengine.AdConditionGroupConfigGetReq) (res *oceanengine.AdConditionGroupConfigGetRes, err error) {
	res = new(oceanengine.AdConditionGroupConfigGetRes)
	res.AdConditionGroupConfigInfoRes, err = service.AdConditionGroupConfig().GetById(ctx, req.Id)
	return
}

// Add 添加广告筛选条件组合配置
func (c *adConditionGroupConfigController) Add(ctx context.Context, req *oceanengine.AdConditionGroupConfigAddReq) (res *oceanengine.AdConditionGroupConfigAddRes, err error) {
	err = service.AdConditionGroupConfig().Add(ctx, req.AdConditionGroupConfigAddReq)
	return
}

// Edit 修改广告筛选条件组合配置
func (c *adConditionGroupConfigController) Edit(ctx context.Context, req *oceanengine.AdConditionGroupConfigEditReq) (res *oceanengine.AdConditionGroupConfigEditRes, err error) {
	err = service.AdConditionGroupConfig().Edit(ctx, req.AdConditionGroupConfigEditReq)
	return
}

// Delete 删除广告筛选条件组合配置
func (c *adConditionGroupConfigController) Delete(ctx context.Context, req *oceanengine.AdConditionGroupConfigDeleteReq) (res *oceanengine.AdConditionGroupConfigDeleteRes, err error) {
	err = service.AdConditionGroupConfig().Delete(ctx, req.Ids)
	return
}
