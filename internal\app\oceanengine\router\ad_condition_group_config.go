// ==========================================================================
// GFast自动生成router操作代码。
// 生成日期：2025-09-11 11:12:41
// 生成路径: internal/app/oceanengine/router/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package router

import (
	"context"

	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/controller"
)

func (router *Router) BindAdConditionGroupConfigController(ctx context.Context, group *ghttp.RouterGroup) {
	group.Group("/adConditionGroupConfig", func(group *ghttp.RouterGroup) {
		group.Bind(
			controller.AdConditionGroupConfig,
		)
	})
}
