// ==========================================================================
// GFast自动生成logic操作代码。
// 生成日期：2025-08-18 17:30:45
// 生成路径: internal/app/ad/logic/adBatchTask/ks_batch_update_account_processor.go
// 生成人：cq
// desc:快手账户批量任务处理器
// company:云南奇讯科技有限公司
// ==========================================================================

package logic

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"github.com/gogf/gf/v2/util/gconv"
	oceanService "github.com/tiger1103/gfast/v3/internal/app/oceanengine/service"
	"github.com/tiger1103/gfast/v3/library/advertiser"
	"github.com/tiger1103/gfast/v3/library/advertiser/toutiao/models"
	"time"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/tiger1103/gfast/v3/internal/app/ad/model"
	"github.com/tiger1103/gfast/v3/internal/app/ad/service"
	commonConsts "github.com/tiger1103/gfast/v3/internal/app/common/consts"
	"github.com/tiger1103/gfast/v3/internal/app/common/logic/channelStream"
)

var projectStreamConsumer *channelStream.StreamConsumer

func init() {
	go StartOceanBatchUpdateProjectTaskConsumer()
}

// StartOceanBatchUpdateProjectTaskConsumer 启动巨量项目批量任务消费者
func StartOceanBatchUpdateProjectTaskConsumer() {
	dev := g.Cfg().MustGet(context.Background(), "redis.channel.stream.env").String()
	config := &channelStream.StreamConsumerConfig{
		StreamName:    fmt.Sprintf(commonConsts.OceanBatchUpdateProjectChannelStream, dev),
		GroupName:     fmt.Sprintf(commonConsts.OceanBatchUpdateProjectChannelGroup1, dev),
		ConsumerName:  fmt.Sprintf(commonConsts.OceanBatchUpdateProjectChannelConsumer1, dev),
		BlockTime:     5 * time.Second,
		BatchSize:     10,
		LockTimeout:   10 * time.Second,
		MessageField:  commonConsts.OceanBatchUpdateProjectMessageField,
		CleanupCron:   "0 0 1 * * ?", // 每天凌晨1点执行清理
		MaxStreamSize: 1000,
	}
	processor := NewOceanBatchUpdateProjectProcessor()
	projectStreamConsumer = channelStream.NewStreamConsumer(config, processor)
	projectStreamConsumer.Start()
}

// OceanBatchUpdateProjectProcessor 巨量项目批量任务处理器
type OceanBatchUpdateProjectProcessor struct{}

// NewOceanBatchUpdateProjectProcessor 创建巨量项目批量任务处理器
func NewOceanBatchUpdateProjectProcessor() *OceanBatchUpdateProjectProcessor {
	return &OceanBatchUpdateProjectProcessor{}
}

// ProcessTask 处理任务
func (s *OceanBatchUpdateProjectProcessor) ProcessTask(ctx context.Context, taskData []byte) (success bool, err error) {
	var task model.BatchUpdateProjectTaskInfo
	if err = json.Unmarshal(taskData, &task); err != nil {
		return false, err
	}

	optResult, err := s.processTask(task)
	if err != nil {
		return false, err
	}

	return optResult == commonConsts.OptResultSuccess, nil
}

// UpdateTaskStatus 更新任务状态
func (s *OceanBatchUpdateProjectProcessor) UpdateTaskStatus(ctx context.Context, taskId string, success bool) error {
	taskEditReq := &model.AdBatchTaskEditReq{
		TaskId: taskId,
	}
	if success {
		taskEditReq.SuccessNum = 1
	} else {
		taskEditReq.FailNum = 1
	}
	return service.AdBatchTask().EditOptStatus(ctx, taskEditReq)
}

// GetTaskId 获取任务ID
func (s *OceanBatchUpdateProjectProcessor) GetTaskId(taskData []byte) (string, error) {
	var task model.KsBatchUpdateAccountTaskInfo
	if err := json.Unmarshal(taskData, &task); err != nil {
		return "", err
	}
	return task.TaskId, nil
}

// processTask 任务处理逻辑
func (s *OceanBatchUpdateProjectProcessor) processTask(task model.BatchUpdateProjectTaskInfo) (optResult string, err error) {
	ctx := context.Background()
	err = g.Try(ctx, func(ctx context.Context) {
		var errMsg string
		accessTokenRes, _ := oceanService.AdAdvertiserAccount().GetAccessTokenByAdvertiserId(ctx, task.AdvertiserId)
		if accessTokenRes.AccessToken == "" {
			errMsg = commonConsts.ErrMsgGetAccessToken
			optResult = commonConsts.OptResultFail
		} else {
			err = s.executeOperation(ctx, accessTokenRes.AccessToken, task)
			if err != nil {
				errMsg = err.Error()
				optResult = commonConsts.OptResultFail
			} else {
				optResult = commonConsts.OptResultSuccess
			}
		}
		// 记录任务详情
		detail := &model.AdBatchTaskDetailAddReq{
			TaskId:       task.TaskId,
			SerialNumber: task.SerialNumber,
			AdvertiserId: task.AdvertiserId,
			ProjectId:    task.ProjectId,
			OptResult:    optResult,
			ErrMsg:       errMsg,
		}
		err = service.AdBatchTaskDetail().Add(ctx, detail)
	})
	return
}

// executeOperation 执行具体的操作
func (s *OceanBatchUpdateProjectProcessor) executeOperation(ctx context.Context, accessToken string, task model.BatchUpdateProjectTaskInfo) error {
	switch task.OptType {
	case commonConsts.OptTypeEnable, commonConsts.OptTypePause:
		return s.updateProjectStatus(ctx, accessToken, task)
	case commonConsts.OptTypeDelete:
		return s.deleteProject(ctx, accessToken, task)
	case commonConsts.OptTypeEditBid:
		return s.updateCpaBid(ctx, accessToken, task)
	case commonConsts.OptTypeEditRoiRatio:
		return nil
	case commonConsts.OptTypeBudget:
		return nil
	case commonConsts.OptTypeEditProjectSchedule:
		return nil
	default:
		return errors.New(commonConsts.ErrMsgUnSupportOptType)
	}
}

// updateProjectStatus 更新项目状态
func (s *OceanBatchUpdateProjectProcessor) updateProjectStatus(ctx context.Context, accessToken string, task model.BatchUpdateProjectTaskInfo) error {
	var optStatus models.ProjectStatusUpdateV30DataOptStatus
	switch task.OptType {
	case commonConsts.OptTypeEnable:
		optStatus = models.ENABLE_ProjectStatusUpdateV30DataOptStatus
	case commonConsts.OptTypePause:
		optStatus = models.DISABLE_ProjectStatusUpdateV30DataOptStatus
	}
	_, err := advertiser.GetToutiaoApiClient().ProjectStatusUpdateV3ApiService.AccessToken(accessToken).
		ProjectStatusUpdateV30Request(models.ProjectStatusUpdateV30Request{
			AdvertiserId: gconv.Int64(task.AdvertiserId),
			Data: []*models.ProjectStatusUpdateV30RequestDataInner{
				{
					ProjectId: gconv.Int64(task.ProjectId),
					OptStatus: optStatus,
				},
			},
		}).Do()
	if err == nil {
		_ = oceanService.AdProject().UpdateOptStatus(ctx, task.ProjectId, gconv.String(optStatus))
	}
	return err
}

// deleteProject 删除项目
func (s *OceanBatchUpdateProjectProcessor) deleteProject(ctx context.Context, accessToken string, task model.BatchUpdateProjectTaskInfo) error {
	_, err := advertiser.GetToutiaoApiClient().ProjectDeleteV3ApiService.AccessToken(accessToken).
		ProjectDeleteV30Request(models.ProjectDeleteV30Request{
			AdvertiserId: gconv.Int64(task.AdvertiserId),
			ProjectIds:   []int64{gconv.Int64(task.ProjectId)},
		}).Do()
	if err == nil {
		_ = oceanService.AdProject().DeleteByProjectId(ctx, task.ProjectId)
	}
	return err
}

// updateCpaBid 修改出价
func (s *OceanBatchUpdateProjectProcessor) updateCpaBid(ctx context.Context, accessToken string, task model.BatchUpdateProjectTaskInfo) error {
	_, err := advertiser.GetToutiaoApiClient().ProjectUpdateV3ApiService.AccessToken(accessToken).
		ProjectUpdateV30Request(models.ProjectUpdateV30Request{
			AdvertiserId: gconv.Int64(task.AdvertiserId),
			DeliverySetting: &models.ProjectUpdateV30RequestDeliverySetting{
				CpaBid: &task.CpaBid,
			},
		}).Do()
	if err == nil {
		_ = oceanService.AdProject().UpdateCpaBid(ctx, task.ProjectId, task.CpaBid)
	}
	return err
}

// updateRoiGoal 修改ROI系数
func (s *OceanBatchUpdateProjectProcessor) updateRoiGoal(ctx context.Context, accessToken string, task model.BatchUpdateProjectTaskInfo) error {
	_, err := advertiser.GetToutiaoApiClient().ProjectRoigoalUpdateV3ApiService.AccessToken(accessToken).
		ProjectRoigoalUpdateV30Request(models.ProjectRoigoalUpdateV30Request{
			AdvertiserId: gconv.Int64(task.AdvertiserId),
			Data: []*models.ProjectRoigoalUpdateV30RequestDataInner{
				{
					ProjectId: gconv.Int64(task.ProjectId),
					RoiGoal:   &task.RoiGoal,
				},
			},
		}).Do()
	if err == nil {
		_ = oceanService.AdProject().DeleteByProjectId(ctx, task.ProjectId)
	}
	return err
}
