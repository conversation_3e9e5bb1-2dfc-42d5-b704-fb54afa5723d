// ==========================================================================
// 通用的 Channel Stream 生产者
// 生成日期：2025-01-15
// desc: 通用的Redis Stream生产者，支持多种channel_stream
// ==========================================================================

package channelStream

import (
	"context"
	"encoding/json"
	"github.com/gogf/gf/v2/os/gtime"

	"github.com/redis/go-redis/v9"
	commonService "github.com/tiger1103/gfast/v3/internal/app/common/service"
	"github.com/tiger1103/gfast/v3/library/liberr"
)

// StreamProducerConfig Stream生产者配置
type StreamProducerConfig struct {
	StreamName   string // Stream名称
	MessageField string // 消息字段名
}

// StreamProducer Stream生产者
type StreamProducer struct {
	config *StreamProducerConfig
}

// NewStreamProducer 创建新的Stream生产者
func NewStreamProducer(config *StreamProducerConfig) *StreamProducer {
	return &StreamProducer{
		config: config,
	}
}

// SendMessage 发送消息到Stream
func (sp *StreamProducer) SendMessage(ctx context.Context, message interface{}) error {
	taskInfo, err := json.Marshal(message)
	if err != nil {
		return err
	}

	err = commonService.GetGoRedis().XAdd(ctx, &redis.XAddArgs{
		Stream: sp.config.StreamName,
		Values: map[string]interface{}{sp.config.MessageField: taskInfo},
	}).Err()

	liberr.ErrIsNil(ctx, err, "写入任务失败")
	return err
}

// SendDelayMessage 发送延迟消息到Stream
func (sp *StreamProducer) SendDelayMessage(ctx context.Context, message interface{}, delaySeconds int64) error {
	taskInfo, err := json.Marshal(message)
	if err != nil {
		return err
	}

	err = commonService.GetGoRedis().XAdd(ctx, &redis.XAddArgs{
		Stream: sp.config.StreamName,
		Values: map[string]interface{}{
			sp.config.MessageField: taskInfo,
			"execute_time":         gtime.Now().Unix() + delaySeconds,
		},
	}).Err()

	liberr.ErrIsNil(ctx, err, "写入任务失败")
	return err
}

// SendBatchMessages 批量发送消息到Stream
func (sp *StreamProducer) SendBatchMessages(ctx context.Context, messages []interface{}) error {
	for _, message := range messages {
		if err := sp.SendMessage(ctx, message); err != nil {
			return err
		}
	}
	return nil
}

// SendBatchDelayMessages 批量发送延迟消息到Stream
func (sp *StreamProducer) SendBatchDelayMessages(ctx context.Context, messages []interface{}, delaySeconds int64) error {
	for _, message := range messages {
		if err := sp.SendDelayMessage(ctx, message, delaySeconds); err != nil {
			return err
		}
	}
	return nil
}
