// ==========================================================================
// GFast自动生成model操作代码。
// 生成日期：2025-06-05 11:33:29
// 生成路径: internal/app/adx/model/adx_material.go
// 生成人：cq
// desc:ADX素材信息表
// company:云南奇讯科技有限公司
// ==========================================================================

package model

import (
	"github.com/gogf/gf/v2/util/gmeta"
	comModel "github.com/tiger1103/gfast/v3/internal/app/common/model"
)

// AdxMaterialInfoRes is the golang structure for table adx_material.
type AdxMaterialInfoRes struct {
	gmeta.Meta     `orm:"table:adx_material"`
	MaterialId     uint64   `orm:"material_id,primary" json:"materialId" dc:"素材ID"`       // 素材ID
	AdType         []string `orm:"ad_type" json:"adType" dc:"广告类型列表"`                     // 广告类型列表
	CommentNum     int64    `orm:"comment_num" json:"commentNum" dc:"评论数"`                // 评论数
	CreateMonth    string   `orm:"create_month" json:"createMonth" dc:"创建年月(格式:YYYY-MM)"` // 创建年月(格式:YYYY-MM)
	DownloadNum    int64    `orm:"download_num" json:"downloadNum" dc:"预估转化量"`            // 预估转化量
	DurationMillis int64    `orm:"duration_millis" json:"durationMillis" dc:"时长(毫秒)"`     // 时长(毫秒)
	ExposureNum    int64    `orm:"exposure_num" json:"exposureNum" dc:"预估曝光量"`            // 预估曝光量
	ForwardNum     int64    `orm:"forward_num" json:"forwardNum" dc:"分享数"`                // 分享数
	LikeNum        int64    `orm:"like_num" json:"likeNum" dc:"点赞数"`                      // 点赞数
	MaterialHeight uint     `orm:"material_height" json:"materialHeight" dc:"素材高度(像素)"`   // 素材高度(像素)
	MaterialType   uint     `orm:"material_type" json:"materialType" dc:"素材类型1图片2视频"`     // 素材类型1图片2视频
	MaterialWidth  uint     `orm:"material_width" json:"materialWidth" dc:"素材宽度(像素)"`     // 素材宽度(像素)
	PicList        []string `orm:"pic_list" json:"picList" dc:"素材封面链接列表"`                 // 素材封面链接列表
	PlayNum        int64    `orm:"play_num" json:"playNum" dc:"播放数"`                      // 播放数
	PlayletId      int64    `orm:"playlet_id" json:"playletId" dc:"投放短剧ID"`
	VideoList      []string `orm:"video_list" json:"videoList" dc:"素材视频链接列表"` // 素材视频链接列表
}

type AdxMaterialListRes struct {
	MaterialId     uint64   `json:"materialId" dc:"素材ID"`
	AdType         []string `json:"adType" dc:"广告类型列表"`
	CommentNum     int64    `json:"commentNum" dc:"评论数"`
	CreateMonth    string   `json:"createMonth" dc:"创建年月(格式:YYYY-MM)"`
	DownloadNum    int64    `json:"downloadNum" dc:"预估转化量"`
	DurationMillis int64    `json:"durationMillis" dc:"时长(毫秒)"`
	ExposureNum    int64    `json:"exposureNum" dc:"预估曝光量"`
	ForwardNum     int64    `json:"forwardNum" dc:"分享数"`
	LikeNum        int64    `json:"likeNum" dc:"点赞数"`
	MaterialHeight uint     `json:"materialHeight" dc:"素材高度(像素)"`
	MaterialType   uint     `json:"materialType" dc:"素材类型1图片2视频"`
	MaterialWidth  uint     `json:"materialWidth" dc:"素材宽度(像素)"`
	PicList        []string `json:"picList" dc:"素材封面链接列表"`
	PlayNum        int64    `json:"playNum" dc:"播放数"`
	PlayletId      int64    `json:"playletId" dc:"投放短剧ID"`
	VideoList      []string `json:"videoList" dc:"素材视频链接列表"`
}

// AdxMaterialSearchReq 分页请求参数
type AdxMaterialSearchReq struct {
	comModel.PageReq
	SearchType    SearchType    `p:"searchType" dc:"搜索类型 1: 全部 2: 产品名 3: 短剧名 4: 公司名 5: 文案 6: 达人"`
	SearchKey     string        `p:"searchKey" dc:"搜索关键字"`
	PreciseSearch PreciseSearch `p:"preciseSearch" dc:"精确搜索 0: 否 1: 是"`
	AdTypes       []string      `p:"adTypes" dc:"广告类型列表 native: 原生 not_native: 非原生 info_flow: 信息流 talent: 达人"`
	MediaTypes    []int         `p:"mediaTypes" dc:"媒体类型 40: 抖音 58: 快手 43: 抖音火山版 1: 今日头条 85: 穿山甲 79: 皮皮虾 97: 番茄小说 38: 西瓜视频 29: 微信"`
	MobileTypes   []int         `p:"mobileTypes" dc:"投放平台 1: 安卓 2: 苹果"`
	SortType      SortType      `p:"sortType" dc:"排序类型 1: 最近出现 2: 最多曝光 3: 最多转化 4: 最多点赞 5: 最多评论 6: 最多分享 7: 最多播放"`
	StartTime     string        `p:"startTime" dc:"开始时间"`
	EndTime       string        `p:"endTime" dc:"结束时间"`
	PlayletId     string        `p:"playletId" dc:"投放短剧ID"`
	MaterialType  uint          `p:"materialType" dc:"素材类型 1图片 2视频"`
}

type SearchType int

const (
	SearchTypeAll           SearchType = 1 // 全部
	SearchTypeProductName   SearchType = 2 // 产品名
	SearchTypePlayletName   SearchType = 3 // 短剧名
	SearchTypePublisherName SearchType = 4 // 公司名
	SearchTypeTitle         SearchType = 5 // 文案
	SearchTypeTalentName    SearchType = 6 // 达人
)

type PreciseSearch int

const (
	PreciseSearchNo  PreciseSearch = 0 // 否
	PreciseSearchYes PreciseSearch = 1 // 是
)

type SortType int

const (
	SortTypeRecentAppearance SortType = 1 // 最近出现
	SortTypeMostExposure     SortType = 2 // 最多曝光
	SortTypeMostConversion   SortType = 3 // 最多转化
	SortTypeMostLike         SortType = 4 // 最多点赞
	SortTypeMostComment      SortType = 5 // 最多评论
	SortTypeMostForward      SortType = 6 // 最多分享
	SortTypeMostPlay         SortType = 7 // 最多播放
)

// AdxMaterialSearchRes 列表返回结果
type AdxMaterialSearchRes struct {
	comModel.ListRes
	List []*AdxMaterialRes `json:"list"`
}

type AdxMaterialRes struct {
	// adx_material
	MaterialId           uint64   `json:"materialId" dc:"素材ID"`
	AdTypes              []string `json:"adTypes" dc:"广告类型列表"`
	CommentNum           int64    `json:"commentNum" dc:"评论数"`
	CreateMonth          string   `json:"createMonth" dc:"创建年月(格式:YYYY-MM)"`
	DownloadNum          int64    `json:"downloadNum" dc:"预估转化量"`
	DurationMillis       int64    `json:"durationMillis" dc:"时长(毫秒)"`
	ExposureNum          int64    `json:"exposureNum" dc:"预估曝光量"`
	ForwardNum           int64    `json:"forwardNum" dc:"分享数"`
	LikeNum              int64    `json:"likeNum" dc:"点赞数"`
	MaterialHeight       uint     `json:"materialHeight" dc:"素材高度(像素)"`
	MaterialType         uint     `json:"materialType" dc:"素材类型 1图片 2视频"`
	MaterialWidth        uint     `json:"materialWidth" dc:"素材宽度(像素)"`
	CreativeMaterialType int      `json:"creative_material_type,omitempty"`
	VideoThumbnailId     string   `orm:"video_thumbnail_id" json:"videoThumbnailId"` // 视频缩略图id
	PicList              []string `json:"picList" dc:"素材封面链接列表"`
	PlayNum              int64    `json:"playNum" dc:"播放数"`
	VideoList            []string `json:"videoList" dc:"素材视频链接列表"`
	// adx_product
	ProductName string `json:"productName" dc:"产品名"`
	ProductIcon string `json:"productIcon" dc:"产品图标"`
	// adx_publisher
	PublisherName string `json:"publisherName" dc:"公司名"`
	// adx_media
	MediaName    string `json:"mediaName" dc:"媒体名称"`
	MediaLogoUrl string `json:"mediaLogoUrl" dc:"媒体logo"`
	// adx_playlet
	PlayletId   int64  `json:"playletId" dc:"投放短剧ID"`
	PlayletName string `json:"playletName" dc:"短剧名"`
	Description string `json:"description" dc:"短剧描述"`
	// adx_creative
	MediaId      int    `json:"mediaId" dc:"媒体ID"`
	PicId        string `json:"picId" dc:"素材ID"`
	PhotoId      string `json:"photo_id"`
	CreativeNum  int64  `json:"creativeNum" dc:"计划数"`
	FirstSeen    string `json:"firstSeen"  dc:"首次出现日期"`
	LastSeen     string `json:"lastSeen"  dc:"最后出现日期"`
	DeliveryDays int    `json:"deliveryDays" dc:"投放天数"`
	Title1       string `json:"title1" dc:"使用的文案"`
	TalentName   string `json:"talentName" dc:"达人名称"`
	MobileType   uint   `json:"mobileType"  dc:"投放平台 1.安卓 2.苹果"`
	ErrorMsg     string `json:"errorMsg" dc:"错误信息"`
}

type AdxMaterialQueryParamRes struct {
	PlayletIds             []int64 `json:"playletIds" dc:"投放短剧ID列表"`
	ProductIds             []int64 `json:"productIds" dc:"产品ID列表"`
	PublisherIds           []int64 `json:"publisherIds" dc:"公司ID列表"`
	MinMaterialId          uint64  `json:"minMaterialId" dc:"最小素材ID"`
	MinProductCreativeId   uint64  `json:"minProductCreativeId" dc:"产品最小创意ID"`
	MinPublisherCreativeId uint64  `json:"minPublisherCreativeId" dc:"公司最小创意ID"`
}

// AdxMaterialAddReq 添加操作请求参数
type AdxMaterialAddReq struct {
	MaterialId     uint64   `p:"materialId" v:"required#主键ID不能为空" dc:"素材ID"`
	AdType         []string `p:"adType" v:"required#广告类型列表不能为空" dc:"广告类型列表"`
	CommentNum     int64    `p:"commentNum"  dc:"评论数"`
	CreateMonth    string   `p:"createMonth"  dc:"创建年月(格式:YYYY-MM)"`
	DownloadNum    int64    `p:"downloadNum"  dc:"预估转化量"`
	DurationMillis int64    `p:"durationMillis"  dc:"时长(毫秒)"`
	ExposureNum    int64    `p:"exposureNum"  dc:"预估曝光量"`
	ForwardNum     int64    `p:"forwardNum"  dc:"分享数"`
	LikeNum        int64    `p:"likeNum"  dc:"点赞数"`
	MaterialHeight uint     `p:"materialHeight"  dc:"素材高度(像素)"`
	MaterialType   uint     `p:"materialType"  dc:"素材类型1图片2视频"`
	MaterialWidth  uint     `p:"materialWidth"  dc:"素材宽度(像素)"`
	PicList        []string `p:"picList" v:"required#素材封面链接列表不能为空" dc:"素材封面链接列表"`
	PlayNum        int64    `p:"playNum"  dc:"播放数"`
	PlayletId      int64    `p:"playletId" dc:"投放短剧ID"`
	VideoList      []string `p:"videoList" v:"required#素材视频链接列表不能为空" dc:"素材视频链接列表"`
}

// AdxMaterialEditReq 修改操作请求参数
type AdxMaterialEditReq struct {
	MaterialId     uint64   `p:"materialId" v:"required#主键ID不能为空" dc:"素材ID"`
	AdType         []string `p:"adType" v:"required#广告类型列表不能为空" dc:"广告类型列表"`
	CommentNum     int64    `p:"commentNum"  dc:"评论数"`
	CreateMonth    string   `p:"createMonth"  dc:"创建年月(格式:YYYY-MM)"`
	DownloadNum    int64    `p:"downloadNum"  dc:"预估转化量"`
	DurationMillis int64    `p:"durationMillis"  dc:"时长(毫秒)"`
	ExposureNum    int64    `p:"exposureNum"  dc:"预估曝光量"`
	ForwardNum     int64    `p:"forwardNum"  dc:"分享数"`
	LikeNum        int64    `p:"likeNum"  dc:"点赞数"`
	MaterialHeight uint     `p:"materialHeight"  dc:"素材高度(像素)"`
	MaterialType   uint     `p:"materialType"  dc:"素材类型1图片2视频"`
	MaterialWidth  uint     `p:"materialWidth"  dc:"素材宽度(像素)"`
	PicList        []string `p:"picList" v:"required#素材封面链接列表不能为空" dc:"素材封面链接列表"`
	PlayNum        int64    `p:"playNum"  dc:"播放数"`
	PlayletId      int64    `p:"playletId" dc:"投放短剧ID"`
	VideoList      []string `p:"videoList" v:"required#素材视频链接列表不能为空" dc:"素材视频链接列表"`
}
