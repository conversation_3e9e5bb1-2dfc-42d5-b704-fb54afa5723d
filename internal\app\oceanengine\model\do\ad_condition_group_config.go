// ==========================================================================
// GFast自动生成model entity操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/model/entity/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package do

import (
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/util/gmeta"
)

// AdConditionGroupConfig is the golang structure for table ad_condition_group_config.
type AdConditionGroupConfig struct {
	gmeta.Meta     `orm:"table:ad_condition_group_config, do:true"`
	Id             interface{} `orm:"id,primary" json:"id"`                  //
	Name           interface{} `orm:"name" json:"name"`                      // 组合名称
	ConditionGroup interface{} `orm:"condition_group" json:"conditionGroup"` // 查询条件组合
	OptType        interface{} `orm:"opt_type" json:"optType"`               // 查询条件组合类型 所有：and 任一：or
	Modules        interface{} `orm:"modules" json:"modules"`                // 操作模块：项目：PROJECT  广告：PROMOTION
	UserId         interface{} `orm:"user_id" json:"userId"`                 // 用户ID
	CreatedAt      *gtime.Time `orm:"created_at" json:"createdAt"`           // 创建时间
	UpdatedAt      *gtime.Time `orm:"updated_at" json:"updatedAt"`           // 更新时间
	DeletedAt      *gtime.Time `orm:"deleted_at" json:"deletedAt"`           // 删除时间
}
