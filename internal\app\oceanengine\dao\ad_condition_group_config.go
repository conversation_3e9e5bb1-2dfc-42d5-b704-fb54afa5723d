// ==========================================================================
// GFast自动生成dao操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/dao/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package dao

import (
	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/dao/internal"
)

// adConditionGroupConfigDao is the manager for logic model data accessing and custom defined data operations functions management.
// You can define custom methods on it to extend its functionality as you wish.
type adConditionGroupConfigDao struct {
	*internal.AdConditionGroupConfigDao
}

var (
	// AdConditionGroupConfig is globally public accessible object for table tools_gen_table operations.
	AdConditionGroupConfig = adConditionGroupConfigDao{
		internal.NewAdConditionGroupConfigDao(),
	}
)

// Fill with you ideas below.
