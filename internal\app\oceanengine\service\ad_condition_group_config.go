// ==========================================================================
// GFast自动生成service操作代码。
// 生成日期：2025-09-11 11:12:40
// 生成路径: internal/app/oceanengine/service/ad_condition_group_config.go
// 生成人：cq
// desc:广告筛选条件组合配置
// company:云南奇讯科技有限公司
// ==========================================================================

package service

import (
	"context"

	"github.com/tiger1103/gfast/v3/internal/app/oceanengine/model"
)

type IAdConditionGroupConfig interface {
	List(ctx context.Context, req *model.AdConditionGroupConfigSearchReq) (res *model.AdConditionGroupConfigSearchRes, err error)
	GetById(ctx context.Context, Id int64) (res *model.AdConditionGroupConfigInfoRes, err error)
	Add(ctx context.Context, req *model.AdConditionGroupConfigAddReq) (err error)
	Edit(ctx context.Context, req *model.AdConditionGroupConfigEditReq) (err error)
	Delete(ctx context.Context, Id []int64) (err error)
}

var localAdConditionGroupConfig IAdConditionGroupConfig

func AdConditionGroupConfig() IAdConditionGroupConfig {
	if localAdConditionGroupConfig == nil {
		panic("implement not found for interface IAdConditionGroupConfig, forgot register?")
	}
	return localAdConditionGroupConfig
}

func RegisterAdConditionGroupConfig(i IAdConditionGroupConfig) {
	localAdConditionGroupConfig = i
}
